'use client'

import React, { useState } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { md5Encrypt, isValidMD5, testMD5Encryption } from '@/lib/crypto-utils'
import { Shield, Check, X } from 'lucide-react'

export default function CryptoTestPage() {
  const [inputText, setInputText] = useState('demo_password')
  const [md5Result, setMd5Result] = useState('')

  const handleEncrypt = () => {
    const encrypted = md5Encrypt(inputText)
    setMd5Result(encrypted)
  }

  const runTests = () => {
    testMD5Encryption()
  }

  const testCases = [
    { input: 'admin', expected: '21232f297a57a5a743894a0e4a801fc3' },
    { input: 'password', expected: '5f4dcc3b5aa765d61d8327deb882cf99' },
    { input: '123456', expected: 'e10adc3949ba59abbe56e057f20f883e' },
    { input: 'demo_password', expected: 'c9a2be50ac796d7c8bff5ab5f6d9a69b' }
  ]

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-6 w-6 text-blue-600" />
            MD5加密测试
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* 交互式测试 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">交互式MD5加密测试</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="input-text">输入文本</Label>
                  <Input
                    id="input-text"
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    placeholder="请输入要加密的文本"
                  />
                </div>
                <div>
                  <Label htmlFor="md5-result">MD5结果</Label>
                  <Input
                    id="md5-result"
                    value={md5Result}
                    readOnly
                    placeholder="MD5加密结果将显示在这里"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button onClick={handleEncrypt}>
                  加密
                </Button>
                <Button onClick={runTests} variant="outline">
                  运行控制台测试
                </Button>
              </div>
              
              {md5Result && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-2">加密结果验证：</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      {md5Result.length === 32 ? (
                        <Check className="h-4 w-4 text-green-600" />
                      ) : (
                        <X className="h-4 w-4 text-red-600" />
                      )}
                      <span>长度: {md5Result.length} 位 {md5Result.length === 32 ? '(正确)' : '(错误)'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {md5Result === md5Result.toLowerCase() ? (
                        <Check className="h-4 w-4 text-green-600" />
                      ) : (
                        <X className="h-4 w-4 text-red-600" />
                      )}
                      <span>小写格式: {md5Result === md5Result.toLowerCase() ? '是' : '否'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {isValidMD5(md5Result) ? (
                        <Check className="h-4 w-4 text-green-600" />
                      ) : (
                        <X className="h-4 w-4 text-red-600" />
                      )}
                      <span>有效的MD5格式: {isValidMD5(md5Result) ? '是' : '否'}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 预定义测试用例 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">预定义测试用例</h3>
              <div className="space-y-2">
                {testCases.map((testCase, index) => {
                  const result = md5Encrypt(testCase.input)
                  const isCorrect = result === testCase.expected
                  
                  return (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">输入: {testCase.input}</div>
                        <div className="text-sm text-gray-600">
                          期望: {testCase.expected}
                        </div>
                        <div className="text-sm text-gray-600">
                          实际: {result}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {isCorrect ? (
                          <Check className="h-5 w-5 text-green-600" />
                        ) : (
                          <X className="h-5 w-5 text-red-600" />
                        )}
                        <span className={`text-sm font-medium ${
                          isCorrect ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {isCorrect ? '通过' : '失败'}
                        </span>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* 说明 */}
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium mb-2 text-blue-800">MD5加密说明</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• MD5加密结果为32位小写十六进制字符串</li>
                <li>• 符合电子水尺API文档要求的密码加密格式</li>
                <li>• 前端使用crypto-js库进行加密</li>
                <li>• 后端使用Python hashlib库进行加密</li>
                <li>• 两端加密结果应该完全一致</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
