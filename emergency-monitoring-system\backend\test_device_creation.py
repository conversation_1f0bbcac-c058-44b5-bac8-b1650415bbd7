#!/usr/bin/env python3
"""
测试设备创建功能
检查电子水尺设备添加失败的原因
"""

import sys
import os
import asyncio
from sqlalchemy.orm import Session

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal, init_db
from app.services.device_service import DeviceService
from app.schemas.device import DeviceCreate
from app.models.user import User

def test_device_creation():
    """测试设备创建功能"""
    print("设备创建测试")
    print("=" * 60)
    
    # 初始化数据库
    init_db()
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 创建或获取测试用户
        test_user = db.query(User).filter(User.username == "admin").first()
        if not test_user:
            print("创建测试用户...")
            from app.core.security import get_password_hash
            test_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("admin123"),
                role="admin",
                is_active=True
            )
            db.add(test_user)
            db.commit()
            db.refresh(test_user)
        
        print(f"测试用户: {test_user.username} (ID: {test_user.id})")
        
        # 创建设备服务
        device_service = DeviceService(db)
        
        # 测试电子水尺设备数据
        device_data = DeviceCreate(
            device_id="TEST_WL001",
            name="测试水尺设备",
            device_type="electronic_water_ruler",
            latitude=39.908823,
            longitude=116.397470,
            location_name="测试位置",
            description="这是一个测试的电子水尺设备",
            sim_card="13800138001",
            alert_water_level_high=5.0,
            data_collection_interval=300,
            is_active=True
        )
        
        print("\n创建设备数据:")
        print(f"设备ID: {device_data.device_id}")
        print(f"设备名称: {device_data.name}")
        print(f"设备类型: {device_data.device_type}")
        print(f"高水位阈值: {device_data.alert_water_level_high}")
        
        # 尝试创建设备
        try:
            device = device_service.create_device(device_data, test_user.id)
            print(f"\n✓ 设备创建成功!")
            print(f"设备ID: {device.id}")
            print(f"设备编号: {device.device_id}")
            print(f"设备名称: {device.name}")
            print(f"创建时间: {device.created_at}")
            
        except Exception as e:
            print(f"\n✗ 设备创建失败: {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()
    
    print("\n" + "=" * 60)
    print("测试完成")

if __name__ == "__main__":
    test_device_creation()
