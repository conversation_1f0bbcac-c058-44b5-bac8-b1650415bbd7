#!/usr/bin/env python3
"""
测试新的电子水尺API流程
验证token获取和设备验证功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.water_level_auth_service import water_level_auth_service

async def test_new_water_level_flow():
    """测试新的电子水尺API流程"""
    print("新电子水尺API流程测试")
    print("=" * 60)
    
    # 测试用户ID
    test_user_id = 1
    
    # 测试凭据
    test_credentials = [
        ("demo_user", "demo_password"),
        ("admin", "admin123"),
        ("test", "test123")
    ]
    
    for username, password in test_credentials:
        print(f"\n测试凭据: {username} / {password}")
        print("-" * 40)
        
        # 测试登录并获取token
        result = await water_level_auth_service.login_and_get_token(
            username, password, test_user_id
        )
        
        print(f"登录结果: {result['success']}")
        
        if result['success']:
            print(f"Token: {result.get('token', 'N/A')[:20]}...")
            print(f"过期时间: {result.get('expires_at', 'N/A')}")
            
            # 测试获取token
            token = water_level_auth_service.get_user_token(test_user_id)
            print(f"获取到的token: {token[:20] if token else 'None'}...")
            
            # 测试刷新token
            refreshed_token = await water_level_auth_service.refresh_token_if_needed(test_user_id)
            print(f"刷新后的token: {refreshed_token[:20] if refreshed_token else 'None'}...")
            
            # 测试凭据状态
            has_credentials = water_level_auth_service.has_user_credentials(test_user_id)
            print(f"有凭据: {has_credentials}")
            
        else:
            print(f"登录失败: {result.get('error', 'Unknown error')}")
            print(f"错误代码: {result.get('error_code', 'N/A')}")
        
        # 清理测试数据
        water_level_auth_service.remove_user_credentials(test_user_id)
    
    print("\n" + "=" * 60)
    print("测试完成")

if __name__ == "__main__":
    asyncio.run(test_new_water_level_flow())
