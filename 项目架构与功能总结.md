# 应急安全监控系统 - 项目架构与功能总结

## 🎯 项目概述

应急安全监控系统是一个专门用于**实时监控汛期水位及森林消防安全**的现代化应急系统。系统集成了摄像头视频流分析、电子水尺数据采集、AI图像识别报警等功能，实现多设备协同监控与可视化预警管理。

### 核心特性
- 🎥 **35个摄像头实时监控** - 支持跨网段设备接入
- 🤖 **AI智能图像识别** - qwen2.5vl模型自动检测异常
- 🌊 **水位监测集成** - 电子水尺数据实时采集
- 🗺️ **地理可视化** - 地图展示设备分布和状态
- ⚡ **实时报警推送** - 多渠道通知，快速响应
- 🔧 **双网络模式** - 开发环境代理模式，生产环境公网模式

## 🏗️ 技术架构

### 整体架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 React    │    │   后端 FastAPI  │    │   数据存储层    │
│                 │    │                 │    │                 │
│ ├─ 监控大屏     │    │ ├─ 设备管理API  │    │ ├─ PostgreSQL   │
│ ├─ 地图可视化   │◄──►│ ├─ 视频流API    │◄──►│ ├─ TimescaleDB  │
│ ├─ 报警管理     │    │ ├─ AI分析API    │    │ ├─ Redis        │
│ ├─ 用户管理     │    │ ├─ 报警推送API  │    │ └─ MinIO        │
│ └─ 系统设置     │    │ └─ WebSocket    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   外部服务层    │              │
         │              │                 │              │
         │              │ ├─ 摄像头SDK    │              │
         │              │ ├─ AI模型服务   │              │
         │              │ ├─ 内网穿透     │              │
         │              │ └─ 第三方API    │              │
         │              └─────────────────┘              │
         └──────────────────────┬──────────────────────────┘
                               │
                    ┌─────────────────┐
                    │   监控设备层    │
                    │                 │
                    │ ├─ 网络摄像头   │
                    │ ├─ 电子水尺     │
                    │ ├─ 传感器设备   │
                    │ └─ 其他IoT设备  │
                    └─────────────────┘
```

### 技术栈详情

#### 后端技术栈
- **核心框架**: FastAPI 0.104+ + Python 3.9+
- **数据库**: PostgreSQL 14+ + TimescaleDB (时序数据)
- **缓存**: Redis 6+ (实时数据缓存、会话管理)
- **消息队列**: Celery + Redis (异步任务处理)
- **摄像头SDK**: NetSDK Python *******
- **AI模型**: qwen2.5vl (图像识别)
- **WebSocket**: FastAPI WebSocket (实时推送)
- **文件存储**: MinIO (图片、视频文件存储)
- **网络适配**: 支持反向代理和公网IP两种模式

#### 前端技术栈
- **框架**: Next.js 15 + React 19 + TypeScript 5
- **UI组件**: Radix UI + Tailwind CSS + Shadcn/ui
- **地图组件**: React-Leaflet + OpenStreetMap
- **视频播放**: HLS.js (支持多画面)
- **图表可视化**: Recharts
- **状态管理**: React Hook Form + Zustand
- **实时通信**: WebSocket Client

## 📁 项目结构

### 后端结构 (`emergency-monitoring-system/backend/`)
```
backend/
├── app/
│   ├── api/                 # API路由层
│   │   ├── v1/             # API版本1
│   │   │   ├── auth.py     # 认证路由
│   │   │   ├── devices.py  # 设备管理路由
│   │   │   ├── cameras.py  # 摄像头路由
│   │   │   ├── water_level.py # 水位监测路由
│   │   │   └── alarms.py   # 报警路由
│   │   └── deps.py         # API依赖注入
│   ├── core/               # 核心配置
│   │   ├── config.py       # 应用配置
│   │   ├── database.py     # 数据库连接
│   │   ├── security.py     # 安全认证
│   │   └── websocket.py    # WebSocket管理
│   ├── models/             # 数据模型
│   │   ├── user.py         # 用户模型
│   │   ├── device.py       # 设备模型
│   │   ├── water_level.py  # 水位监测模型
│   │   └── alarm.py        # 报警模型
│   ├── services/           # 业务逻辑层
│   │   ├── camera_service.py
│   │   ├── water_level_service.py
│   │   ├── ai_service.py
│   │   └── auth_service.py
│   ├── sdk/               # 摄像头SDK封装
│   │   ├── camera_client.py
│   │   └── sdk_manager.py
│   └── utils/             # 工具类
│       └── logger.py
├── alembic/               # 数据库迁移
├── requirements.txt       # Python依赖
└── main.py               # 应用入口
```

### 前端结构 (`emergency-monitoring-system/frontend/`)
```
frontend/
├── app/                   # Next.js App Router
│   ├── dashboard/         # 监控大屏
│   ├── device-management/ # 设备管理
│   ├── realtime-monitoring/ # 实时监控
│   ├── water-level/       # 水位监测
│   ├── analytics/         # 数据分析
│   ├── settings/          # 系统设置
│   └── api/              # API路由
│       ├── cameras/      # 摄像头API
│       └── v1/          # API版本1
├── components/           # 可复用组件
│   ├── ui/              # 基础UI组件
│   ├── charts/          # 图表组件
│   ├── maps/            # 地图组件
│   └── video/           # 视频组件
├── hooks/               # 自定义Hooks
├── lib/                 # 工具库
├── styles/              # 样式文件
├── package.json         # 依赖配置
└── next.config.mjs      # Next.js配置
```

## 🚀 核心功能模块

### 1. 设备管理系统
- **多设备支持**: 35个摄像头 + 电子水尺设备
- **跨网段接入**: 支持不同网络环境的设备接入
- **设备状态监控**: 实时监控设备在线状态、健康检查
- **设备分组管理**: 按区域、功能或类型进行设备分组
- **设备配置**: 远程配置设备参数和工作模式

### 2. 视频监控系统
- **多路视频流**: 支持RTSP/HLS协议，同时处理多路视频
- **视频墙显示**: 多画面同时监控，支持画面切换
- **录像回放**: 历史视频查看和回放功能
- **抓图功能**: 定时或手动抓图，支持图片存储
- **流媒体优化**: 支持不同码率和分辨率适配

### 3. AI智能分析
- **qwen2.5vl模型**: 先进的视觉语言模型进行图像分析
- **异常检测**: 自动识别火灾、洪水等安全隐患
- **智能报警**: 基于AI分析结果的智能预警机制
- **分析报告**: AI分析结果统计和趋势分析
- **模型配置**: 支持AI模型参数调优和提示词配置

### 4. 水位监测系统
- **实时数据采集**: 电子水尺数据实时采集和处理
- **阈值报警**: 高低水位预警，支持多级报警
- **历史趋势**: 水位变化图表和趋势分析
- **数据导出**: 监测数据报表导出功能
- **环境监测**: 温度、湿度、气压等环境数据采集

### 5. 地理可视化
- **地图展示**: 基于OpenStreetMap的设备分布可视化
- **实时状态**: 设备在线状态、报警状态地图显示
- **报警定位**: 报警事件在地图上的精确定位
- **区域管理**: 监控区域划分和管理
- **轨迹回放**: 移动设备轨迹回放功能

### 6. 报警管理系统
- **多级报警**: 不同严重程度的报警分级处理
- **实时推送**: 基于WebSocket的实时报警通知
- **报警处理**: 完整的确认、处理、关闭流程
- **历史记录**: 报警事件的完整追踪和统计
- **通知渠道**: 支持多种通知方式（邮件、短信、推送）

## 🔧 网络架构特色

### 双网络模式支持
1. **开发环境模式**
   - 代理模式访问，支持跨网段设备
   - 本地开发调试友好
   - 支持热重载和实时调试

2. **生产环境模式**
   - 公网IP直接访问
   - 高性能、低延迟
   - 支持负载均衡和高可用

### 内网穿透方案
项目提供多种内网穿透解决方案：
- **FRP**: 自建服务器穿透，成本低、可控性强
- **NATAPP**: 商业穿透服务，稳定可靠
- **Ngrok**: 国外穿透服务，功能丰富
- **花生壳**: 国内穿透服务，本土化支持

## 📊 数据库设计

### 核心数据表结构

#### 用户管理模块
```sql
-- 用户基本信息
User: id, username, email, full_name, hashed_password, is_active, is_superuser
-- 角色权限
Role: id, name, description, permissions
-- 用户会话
UserSession: id, user_id, session_token, expires_at
```

#### 设备管理模块
```sql
-- 设备基本信息
Device: id, name, type, ip_address, port, location, status
-- 设备分组
DeviceGroup: id, name, description, parent_group_id
-- 设备状态日志
DeviceStatusLog: id, device_id, status, timestamp, details
```

#### 水位监测模块
```sql
-- 水位设备
WaterLevelDevice: id, device_name, location, alert_high, alert_low
-- 水位数据记录
WaterLevelData: id, device_id, water_level, temperature, humidity, timestamp
-- 水位报警
WaterLevelAlert: id, device_id, alert_type, alert_level, timestamp
```

#### 报警管理模块
```sql
-- 报警记录
Alarm: id, device_id, alarm_type, severity, status, created_at
-- 报警处理记录
AlarmHandling: id, alarm_id, handler_id, action, handled_at
```

## 🚀 部署方案

### Docker容器化部署
```yaml
# docker-compose.yml 主要服务
services:
  api:          # FastAPI后端服务 (端口8000)
  frontend:     # Next.js前端服务 (端口3000)
  postgres:     # PostgreSQL + TimescaleDB (端口5432)
  redis:        # Redis缓存 (端口6379)
  celery-worker:# Celery异步任务处理
  celery-beat:  # Celery定时任务调度
  minio:        # MinIO对象存储 (端口9000/9001)
```

### 多环境配置
- **开发环境**: `docker-compose.dev.yml` - 开发调试优化
- **生产环境**: `docker-compose.prod.yml` - 生产性能优化
- **4G测试环境**: `docker-compose.4g-test.yml` - 4G网络测试
- **流媒体环境**: `docker-compose.streaming.yml` - 视频流优化

### 快速部署命令
```bash
# 生产环境一键部署
git clone <repository-url>
cd emergency-monitoring-system
cp .env.prod.example .env
docker-compose up -d

# 开发环境搭建
./scripts/setup-config.sh dev
./scripts/dev-setup.sh
./scripts/dev-start.sh
```

## 📈 项目状态

### 开发进度
- ✅ **第一阶段**: 基础架构搭建 (100%)
- 🚧 **第二阶段**: 摄像头集成与视频功能 (进行中)
- 📋 **第三阶段**: AI分析与报警系统 (计划中)
- 📋 **第四阶段**: 地图可视化与监控大屏 (计划中)
- 📋 **第五阶段**: 系统优化与部署 (计划中)

### 功能模块完成度
| 模块 | 状态 | 完成度 | 负责团队 |
|------|------|--------|----------|
| 用户管理 | ✅ 完成 | 100% | 后端团队 |
| 设备管理 | ✅ 完成 | 100% | 后端团队 |
| 摄像头集成 | ✅ 完成 | 100% | 后端团队 |
| 视频流播放 | 🚧 开发中 | 40% | 前端团队 |
| 地图可视化 | ✅ 完成 | 100% | 前端团队 |
| AI图像识别 | 📋 计划中 | 0% | 后端团队 |
| 报警系统 | 📋 计划中 | 0% | 全栈团队 |
| 监控大屏 | 📋 计划中 | 0% | 前端团队 |

## 🎯 项目亮点

### 技术亮点
1. **现代化技术栈** - 采用最新的React 19、Next.js 15、FastAPI等前沿技术
2. **微服务架构** - 前后端分离，服务解耦，易于维护和扩展
3. **实时性强** - WebSocket实时通信，毫秒级响应
4. **类型安全** - 全栈TypeScript，减少运行时错误
5. **容器化部署** - Docker容器化，一键部署，环境一致性

### 业务亮点
1. **多设备集成** - 支持35个摄像头和多种传感器设备
2. **AI智能分析** - 集成先进的视觉语言模型
3. **跨网段支持** - 灵活的网络架构适应复杂环境
4. **实时监控** - 7x24小时不间断监控
5. **可视化展示** - 直观的地图和图表展示

### 架构亮点
1. **扩展性好** - 模块化设计，易于功能扩展
2. **高可用性** - 支持负载均衡和故障转移
3. **网络适应性** - 支持多种网络环境和穿透方案
4. **数据安全** - 完善的认证授权和数据加密
5. **监控完善** - 全方位的系统监控和日志记录

## 🔮 未来规划

### 短期目标 (1-3个月)
- 完成视频流播放功能
- 集成AI图像识别模型
- 实现基础报警系统
- 优化系统性能

### 中期目标 (3-6个月)
- 完善监控大屏功能
- 增加移动端支持
- 实现数据分析和报表
- 集成更多传感器类型

### 长期目标 (6-12个月)
- 支持边缘计算
- 实现预测性维护
- 集成第三方系统
- 支持多租户架构

---

**文档版本**: v1.0.0  
**最后更新**: 2025-01-09  
**项目状态**: 开发中 🚧  
**技术负责人**: [待填写]  
**联系方式**: [待填写]
