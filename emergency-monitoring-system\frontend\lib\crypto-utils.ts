/**
 * 加密工具函数
 * Crypto utility functions
 */

import CryptoJS from 'crypto-js'

/**
 * MD5加密函数
 * 返回32位小写十六进制字符串，符合电子水尺API要求
 * @param text 要加密的文本
 * @returns 32位小写MD5哈希值
 */
export function md5Encrypt(text: string): string {
  return CryptoJS.MD5(text).toString().toLowerCase()
}

/**
 * 验证MD5哈希值格式
 * @param hash MD5哈希值
 * @returns 是否为有效的32位小写十六进制字符串
 */
export function isValidMD5(hash: string): boolean {
  return /^[a-f0-9]{32}$/.test(hash)
}

/**
 * 测试MD5加密功能
 */
export function testMD5Encryption() {
  const testCases = [
    'admin',
    'password',
    '123456',
    'demo_password'
  ]
  
  console.log('前端MD5加密测试:')
  testCases.forEach(password => {
    const encrypted = md5Encrypt(password)
    console.log(`${password} -> ${encrypted} (${encrypted.length}位, 小写: ${encrypted === encrypted.toLowerCase()})`)
  })
}
