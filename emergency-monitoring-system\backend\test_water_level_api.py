#!/usr/bin/env python3
"""
测试电子水尺API功能
验证MD5加密和API调用流程
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.water_level_service import WaterLevelAPIClient

async def test_water_level_api():
    """测试电子水尺API功能"""
    print("电子水尺API测试")
    print("=" * 60)
    
    # 创建API客户端
    client = WaterLevelAPIClient()
    
    # 测试用例
    test_credentials = [
        ("demo_user", "demo_password"),
        ("admin", "admin123"),
        ("test", "test123")
    ]
    
    for username, password in test_credentials:
        print(f"\n测试凭据: {username} / {password}")
        print("-" * 40)
        
        # 测试登录
        login_result = await client.login(username, password, password_already_encrypted=False)
        print(f"登录结果: {login_result['success']}")
        
        if login_result['success']:
            print(f"Token: {login_result.get('token', 'N/A')[:20]}...")
            
            # 测试获取设备列表
            devices_result = await client.get_devices()
            print(f"设备列表获取: {devices_result['success']}")
            print(f"设备数量: {devices_result.get('count', 0)}")
            
            if devices_result['success'] and devices_result.get('data'):
                # 测试设备验证
                first_device = devices_result['data'][0]
                device_id = first_device.get('did')
                if device_id:
                    verify_result = await client.verify_device(device_id, username, password, password_already_encrypted=False)
                    print(f"设备验证 ({device_id}): {verify_result['success']}")
        else:
            print(f"登录失败: {login_result.get('error', 'Unknown error')}")
    
    print("\n" + "=" * 60)
    print("测试完成")

if __name__ == "__main__":
    asyncio.run(test_water_level_api())
