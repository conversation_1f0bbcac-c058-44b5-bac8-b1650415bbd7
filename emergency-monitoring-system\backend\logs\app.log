2025-08-01 15:38:28 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-01 16:12:21 | WARNING  | app.services.auth_service:authenticate_user:28 | 登录失败: 用户不存在 - admin
2025-08-01 16:12:21 | WARNING  | app.api.v1.auth:login:32 | 登录失败: admin - IP: 127.0.0.1
2025-08-01 16:12:28 | WARNING  | app.services.auth_service:authenticate_user:28 | 登录失败: 用户不存在 - admin
2025-08-01 16:12:28 | WARNING  | app.api.v1.auth:login:32 | 登录失败: admin - IP: 127.0.0.1
2025-08-01 16:12:43 | WARNING  | app.services.auth_service:authenticate_user:28 | 登录失败: 用户不存在 - admin
2025-08-01 16:12:43 | WARNING  | app.api.v1.auth:login:32 | 登录失败: admin - IP: 127.0.0.1
2025-08-01 16:12:56 | WARNING  | app.services.auth_service:authenticate_user:28 | 登录失败: 用户不存在 - admin
2025-08-01 16:12:56 | WARNING  | app.api.v1.auth:login:32 | 登录失败: admin - IP: 127.0.0.1
2025-08-01 16:14:16 | WARNING  | app.services.auth_service:authenticate_user:28 | 登录失败: 用户不存在 - admin
2025-08-01 16:14:16 | WARNING  | app.api.v1.auth:login:32 | 登录失败: admin - IP: 127.0.0.1
2025-08-01 16:14:49 | WARNING  | app.services.auth_service:authenticate_user:28 | 登录失败: 用户不存在 - admin
2025-08-01 16:14:49 | WARNING  | app.api.v1.auth:login:32 | 登录失败: admin - IP: 127.0.0.1
2025-08-01 16:16:44 | WARNING  | app.services.auth_service:authenticate_user:28 | 登录失败: 用户不存在 - admin
2025-08-01 16:16:44 | WARNING  | app.api.v1.auth:login:32 | 登录失败: admin - IP: 127.0.0.1
2025-08-01 16:18:49 | WARNING  | logging:callHandlers:1736 | (trapped) error reading bcrypt version
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=400, pipe_handle=536)
                                      │           └ <function spawn_main at 0x000002093F38CAE0>
                                      └ <function spawn_main at 0x000002093F38CAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 288
               │     └ 3
               └ <function _main at 0x000002093F38CB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 288
           │    └ <function BaseProcess._bootstrap at 0x000002093F0A3560>
           └ <SpawnProcess name='SpawnProcess-1' parent=400 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000002093F0493A0>
    └ <SpawnProcess name='SpawnProcess-1' parent=400 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002094148B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=400 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=400 started>
    │    └ <function subprocess_started at 0x000002094158F4C0>
    └ <SpawnProcess name='SpawnProcess-1' parent=400 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=576, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002094148B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=576, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002094158E3E0>
           │       │   └ <uvicorn.server.Server object at 0x000002094148B380>
           │       └ <function run at 0x000002093F38DD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000209415426C0>
           │      └ <function Runner.run at 0x000002094124EF20>
           └ <asyncio.runners.Runner object at 0x00000209415F41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002094124CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000209415F41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002094124C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002094124E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020940D75080>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x00000209452B2120>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602300>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000020944948EC0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x00000209452B2120>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602300>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602300>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000209452B2A50>
          └ <fastapi.applications.FastAPI object at 0x0000020944948EC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000020945608CC0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000209452B2900>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000209452B2A50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000020945608CC0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000209452B27B0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000209452B2900>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000020945608CC0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000209452AAE60>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002094544DE50>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000209452B27B0>
          └ <function wrap_app_handling_exceptions at 0x0000020942676020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020945609440>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002094544DE50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020945609440>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002094544DE50>>
          └ <fastapi.routing.APIRouter object at 0x000002094544DE50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020945609440>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x0000020942677920>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020945609440>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000020945501F80>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020945609440>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000209452A9860>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000020945609080>
          └ <function wrap_app_handling_exceptions at 0x0000020942676020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020945608FE0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020945602...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000020945609080>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000209452A9860>
                     └ <function get_request_handler.<locals>.app at 0x0000020945501EE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000020942675620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.orm.session.Session object at 0x0000020945612250>, 'user_login': UserLogin(username='admin', password='adm...
                 │         └ <function login at 0x00000209445718A0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "E:\yingj\emergency-monitoring-system\backend\app\api\v1\auth.py", line 26, in login
    user = auth_service.authenticate_user(
           │            └ <function AuthService.authenticate_user at 0x0000020944936840>
           └ <app.services.auth_service.AuthService object at 0x0000020945610160>

  File "E:\yingj\emergency-monitoring-system\backend\app\services\auth_service.py", line 35, in authenticate_user
    if not verify_password(password, user.hashed_password):
           │               │         │    └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x0000020944573BA0>
           │               │         └ <app.models.user.User object at 0x00000209452B30E0>
           │               └ 'admin123'
           └ <function verify_password at 0x00000209448CBA60>

  File "E:\yingj\emergency-monitoring-system\backend\app\core\security.py", line 38, in verify_password
    return pwd_context.verify(plain_password, hashed_password)
           │           │      │               └ '$2b$12$GtgKs7qt2v8AvUo.XXN5GOLVH1JuOVYklji/BLasQZsZHkr4mjp1G'
           │           │      └ 'admin123'
           │           └ <function CryptContext.verify at 0x00000209448C3BA0>
           └ <CryptContext at 0x209447eecf0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\context.py", line 2347, in verify
    return record.verify(secret, hash, **kwds)
           │      │      │       │       └ {}
           │      │      │       └ '$2b$12$GtgKs7qt2v8AvUo.XXN5GOLVH1JuOVYklji/BLasQZsZHkr4mjp1G'
           │      │      └ 'admin123'
           │      └ <classmethod(<function GenericHandler.verify at 0x00000209448AD440>)>
           └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 792, in verify
    return consteq(self._calc_checksum(secret), chk)
           │       │    │              │        └ 'LVH1JuOVYklji/BLasQZsZHkr4mjp1G'
           │       │    │              └ 'admin123'
           │       │    └ <function _NoBackend._calc_checksum at 0x00000209448CA8E0>
           │       └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x00000209452B2F90>
           └ <built-in function compare_digest>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
    │    └ <classmethod(<function BackendMixin._stub_requires_backend at 0x00000209448AF740>)>
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x00000209452B2F90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
    │   └ <classmethod(<function BackendMixin.set_backend at 0x00000209448AF4C0>)>
    └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           │     │           │            └ False
           │     │           └ 'any'
           │     └ <classmethod(<function BackendMixin.set_backend at 0x00000209448AF4C0>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2163, in set_backend
    return cls.set_backend(name, dryrun=dryrun)
           │   │           │            └ False
           │   │           └ 'bcrypt'
           │   └ <classmethod(<function BackendMixin.set_backend at 0x00000209448AF4C0>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2188, in set_backend
    cls._set_backend(name, dryrun)
    │   │            │     └ False
    │   │            └ 'bcrypt'
    │   └ <classmethod(<function SubclassBackendMixin._set_backend at 0x00000209448AF880>)>
    └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2311, in _set_backend
    super(SubclassBackendMixin, cls)._set_backend(name, dryrun)
          │                     │                 │     └ False
          │                     │                 └ 'bcrypt'
          │                     └ <class 'passlib.handlers.bcrypt.bcrypt'>
          └ <class 'passlib.utils.handlers.SubclassBackendMixin'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2224, in _set_backend
    ok = loader(**kwds)
         │        └ {'name': 'bcrypt', 'dryrun': False}
         └ <bound method _BcryptBackend._load_backend_mixin of <class 'passlib.handlers.bcrypt._BcryptBackend'>>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              └ <module 'bcrypt' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bcrypt\\__i...

AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 16:18:50 | DEBUG    | logging:callHandlers:1736 | detected 'bcrypt' backend, version '<unknown>'
2025-08-01 16:18:50 | DEBUG    | logging:callHandlers:1736 | 'bcrypt' backend lacks $2$ support, enabling workaround
2025-08-01 16:18:50 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-01 16:18:50 | INFO     | app.api.v1.auth:login:55 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-01 16:19:04 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-01 16:19:04 | INFO     | app.api.v1.auth:login:55 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-01 16:52:28 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-01 16:52:28 | INFO     | app.api.v1.auth:login:55 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 00:42:00 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:08:21 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:20:41 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:20:41 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:21:08 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:21:08 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:24:55 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:24:56 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:24:57 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:25:11 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:25:13 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:25:13 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:25:31 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:25:33 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:25:33 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:26:16 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:26:16 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:29:14 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:29:16 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:29:16 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:29:33 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:29:35 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:29:35 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:29:56 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:29:58 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:29:58 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:30:16 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:30:18 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:30:18 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:30:29 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:30:31 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:30:31 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:30:43 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('192.168.2.107', 54027)
2025-08-03 13:30:43 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('192.168.2.107', 54027)
2025-08-03 13:31:59 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:32:01 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:32:01 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:32:14 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:32:15 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:32:16 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:32:27 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:32:29 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:32:29 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:32:39 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:32:41 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:32:41 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:32:52 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:32:53 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:32:53 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:33:05 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:34:52 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:34:53 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:35:12 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:35:12 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:38:45 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:38:47 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:38:47 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:38:59 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:39:01 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:39:01 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:39:14 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:39:16 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:39:16 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:51:49 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:51:51 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:51:51 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:52:06 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:52:08 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:52:09 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 13:59:55 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 13:59:57 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 13:59:57 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 14:00:16 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 14:00:18 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 14:00:18 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器(TCP)启动成功: 0.0.0.0:9500
2025-08-03 14:01:34 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 14:01:36 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 14:01:36 | ERROR    | logging:callHandlers:1736 | 启动注册服务器失败: 'CameraRegistrationServer' object has no attribute 'protocol'
2025-08-03 14:01:36 | ERROR    | logging:callHandlers:1736 | Task exception was never retrieved
future: <Task finished name='Task-3' coro=<CameraRegistrationServer.start_server() done, defined at E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py:53> exception=AttributeError("'CameraRegistrationServer' object has no attribute 'protocol'")>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=1048, pipe_handle=556)
                                      │           └ <function spawn_main at 0x000002085BA3CAE0>
                                      └ <function spawn_main at 0x000002085BA3CAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 188
               │     └ 3
               └ <function _main at 0x000002085BA3CB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 188
           │    └ <function BaseProcess._bootstrap at 0x000002085B753560>
           └ <SpawnProcess name='SpawnProcess-9' parent=1048 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000002085B6F93A0>
    └ <SpawnProcess name='SpawnProcess-9' parent=1048 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002085DB5B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-9' parent=1048 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-9' parent=1048 started>
    │    └ <function subprocess_started at 0x000002085DC5F4C0>
    └ <SpawnProcess name='SpawnProcess-9' parent=1048 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=568, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002085DB5B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=568, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002085DC5E3E0>
           │       │   └ <uvicorn.server.Server object at 0x000002085DB5B380>
           │       └ <function run at 0x000002085BA3DD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002085DC126C0>
           │      └ <function Runner.run at 0x000002085D93EF20>
           └ <asyncio.runners.Runner object at 0x000002085DCC41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002085D93CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002085DCC41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002085D93C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002085D93E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2025, in _run_once
    handle = self._ready.popleft()
             │    │      └ <method 'popleft' of 'collections.deque' objects>
             │    └ deque([])
             └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.

> File "E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py", line 56, in start_server
    if self.protocol == "tcp":
       └ <app.services.camera_registration_server.CameraRegistrationServer object at 0x0000020861B48EC0>

AttributeError: 'CameraRegistrationServer' object has no attribute 'protocol'
2025-08-03 14:01:55 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 14:01:56 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 14:01:56 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 14:31:53 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 14:32:34 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 14:38:58 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 14:38:58 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 14:44:15 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58250)
2025-08-03 14:44:15 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58250)
2025-08-03 14:44:29 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58262)
2025-08-03 14:44:29 | INFO     | logging:callHandlers:1736 | 摄像头注册成功: ID=12345, IP=TEST_CAMERA:37777
2025-08-03 14:44:29 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58262)
2025-08-03 14:46:56 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58368)
2025-08-03 14:46:56 | INFO     | logging:callHandlers:1736 | 摄像头注册成功: ID=99999, IP=TEST_CAMERA:37777
2025-08-03 14:46:56 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58368)
2025-08-03 14:47:59 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:48:59 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:49:59 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:49:59 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 99999
2025-08-03 14:50:59 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:50:59 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 99999
2025-08-03 14:51:59 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:51:59 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 99999
2025-08-03 14:52:28 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 14:52:34 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 14:52:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:52:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 99999
2025-08-03 14:53:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:53:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 99999
2025-08-03 14:54:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:54:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 99999
2025-08-03 14:55:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:55:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 99999
2025-08-03 14:56:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:56:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 99999
2025-08-03 14:57:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:57:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 99999
2025-08-03 14:58:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:58:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 99999
2025-08-03 14:59:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 12345
2025-08-03 14:59:34 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 99999
2025-08-03 20:54:10 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 20:54:10 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 21:01:35 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 21:01:37 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 21:01:37 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 21:03:49 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 21:03:51 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 21:03:51 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 21:04:07 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 21:04:09 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 21:04:09 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 21:05:06 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 21:05:08 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 21:05:08 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 21:10:02 | WARNING  | app.services.auth_service:authenticate_user:28 | 登录失败: 用户不存在 - 13800138000
2025-08-03 21:10:02 | WARNING  | app.api.v1.auth:login:73 | 登录失败: 13800138000 - IP: 127.0.0.1
2025-08-03 21:15:33 | WARNING  | logging:callHandlers:1736 | (trapped) error reading bcrypt version
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=20752, pipe_handle=564)
                                      │           └ <function spawn_main at 0x0000022DE26ACAE0>
                                      └ <function spawn_main at 0x0000022DE26ACAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 324
               │     └ 3
               └ <function _main at 0x0000022DE26ACB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 324
           │    └ <function BaseProcess._bootstrap at 0x0000022DE23C3560>
           └ <SpawnProcess name='SpawnProcess-5' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000022DE23693A0>
    └ <SpawnProcess name='SpawnProcess-5' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000022DE479B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-5' parent=20752 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-5' parent=20752 started>
    │    └ <function subprocess_started at 0x0000022DE489F4C0>
    └ <SpawnProcess name='SpawnProcess-5' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=564, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000022DE479B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=564, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000022DE489E3E0>
           │       │   └ <uvicorn.server.Server object at 0x0000022DE479B380>
           │       └ <function run at 0x0000022DE26ADD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000022DE48526C0>
           │      └ <function Runner.run at 0x0000022DE454EF20>
           └ <asyncio.runners.Runner object at 0x0000022DE49041A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000022DE454CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000022DE49041A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000022DE454C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000022DE454E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000022DE4075080>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000022DE87A2E40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4DE10>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4D...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000022DE7DB5010>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000022DE87A2E40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4DE10>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4D...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4DE10>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4D...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000022DE87A3770>
          └ <fastapi.applications.FastAPI object at 0x0000022DE7DB5010>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000022DE8BB3C40>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4D...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000022DE87A3620>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000022DE87A3770>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000022DE8BB3C40>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4D...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000022DE87A34D0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000022DE87A3620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000022DE8BB3C40>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4D...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000022DE8B9D950>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000022DE8947550>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000022DE87A34D0>
          └ <function wrap_app_handling_exceptions at 0x0000022DE598A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000022DE8B51620>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4D...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000022DE8947550>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000022DE8B51620>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4D...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000022DE8947550>>
          └ <fastapi.routing.APIRouter object at 0x0000022DE8947550>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000022DE8B51620>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4D...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x0000022DE598B920>
          └ APIRoute(path='/api/v1/auth/register', name='register', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000022DE8B51620>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4D...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000022DE8A3CEA0>
          └ APIRoute(path='/api/v1/auth/register', name='register', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000022DE8B51620>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4D...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000022DE8B51590>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000022DE8B51F80>
          └ <function wrap_app_handling_exceptions at 0x0000022DE598A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000022DE8B514E0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000022DE8B4D...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000022DE8B51F80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000022DE8B51590>
                     └ <function get_request_handler.<locals>.app at 0x0000022DE8A3CE00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000022DE5989620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.orm.session.Session object at 0x0000022DE8B43A70>, 'user_create': UserCreate(username='testuser', email='t...
                 │         └ <function register at 0x0000022DE79E7D80>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "E:\yingj\emergency-monitoring-system\backend\app\api\v1\auth.py", line 40, in register
    user = auth_service.create_user(user_create)
           │            │           └ UserCreate(username='testuser', email='<EMAIL>', full_name='测试用户', phone='***********', department=None, position=No...
           │            └ <function AuthService.create_user at 0x0000022DE7DC89A0>
           └ <app.services.auth_service.AuthService object at 0x0000022DE8B68F50>

  File "E:\yingj\emergency-monitoring-system\backend\app\services\auth_service.py", line 260, in create_user
    hashed_password=get_password_hash(user_create.password),
                    │                 │           └ '123456'
                    │                 └ UserCreate(username='testuser', email='<EMAIL>', full_name='测试用户', phone='***********', department=None, position=No...
                    └ <function get_password_hash at 0x0000022DE7D4A3E0>

  File "E:\yingj\emergency-monitoring-system\backend\app\core\security.py", line 42, in get_password_hash
    return pwd_context.hash(password)
           │           │    └ '123456'
           │           └ <function CryptContext.hash at 0x0000022DE7D363E0>
           └ <CryptContext at 0x22de7c4af90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\context.py", line 2258, in hash
    return record.hash(secret, **kwds)
           │      │    │         └ {}
           │      │    └ '123456'
           │      └ <classmethod(<function GenericHandler.hash at 0x0000022DE7CFFC40>)>
           └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 779, in hash
    self.checksum = self._calc_checksum(secret)
    │    │          │    │              └ '123456'
    │    │          │    └ <function _NoBackend._calc_checksum at 0x0000022DE7D491C0>
    │    │          └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x0000022DE87A3E00>
    │    └ None
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x0000022DE87A3E00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
    │    └ <classmethod(<function BackendMixin._stub_requires_backend at 0x0000022DE7D2A020>)>
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x0000022DE87A3E00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
    │   └ <classmethod(<function BackendMixin.set_backend at 0x0000022DE7D29DA0>)>
    └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           │     │           │            └ False
           │     │           └ 'any'
           │     └ <classmethod(<function BackendMixin.set_backend at 0x0000022DE7D29DA0>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2163, in set_backend
    return cls.set_backend(name, dryrun=dryrun)
           │   │           │            └ False
           │   │           └ 'bcrypt'
           │   └ <classmethod(<function BackendMixin.set_backend at 0x0000022DE7D29DA0>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2188, in set_backend
    cls._set_backend(name, dryrun)
    │   │            │     └ False
    │   │            └ 'bcrypt'
    │   └ <classmethod(<function SubclassBackendMixin._set_backend at 0x0000022DE7D2A160>)>
    └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2311, in _set_backend
    super(SubclassBackendMixin, cls)._set_backend(name, dryrun)
          │                     │                 │     └ False
          │                     │                 └ 'bcrypt'
          │                     └ <class 'passlib.handlers.bcrypt.bcrypt'>
          └ <class 'passlib.utils.handlers.SubclassBackendMixin'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2224, in _set_backend
    ok = loader(**kwds)
         │        └ {'name': 'bcrypt', 'dryrun': False}
         └ <bound method _BcryptBackend._load_backend_mixin of <class 'passlib.handlers.bcrypt._BcryptBackend'>>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              └ <module 'bcrypt' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bcrypt\\__i...

AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-03 21:15:34 | DEBUG    | logging:callHandlers:1736 | detected 'bcrypt' backend, version '<unknown>'
2025-08-03 21:15:34 | DEBUG    | logging:callHandlers:1736 | 'bcrypt' backend lacks $2$ support, enabling workaround
2025-08-03 21:15:34 | INFO     | app.services.auth_service:create_user:277 | 创建用户成功: testuser
2025-08-03 21:15:34 | INFO     | app.api.v1.auth:register:42 | 用户注册成功: testuser
2025-08-03 21:15:36 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 21:15:36 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 21:36:31 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 21:36:31 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 21:38:03 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 21:38:03 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 21:39:20 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 21:39:22 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 21:39:22 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 21:39:39 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 21:39:41 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 21:39:41 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 21:40:03 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 21:40:04 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 21:40:05 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 21:40:24 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 21:40:26 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 21:40:26 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 21:41:11 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 21:41:13 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 21:41:13 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 21:41:30 | WARNING  | logging:callHandlers:1736 | (trapped) error reading bcrypt version
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=20752, pipe_handle=564)
                                      │           └ <function spawn_main at 0x0000020C2EC0CAE0>
                                      └ <function spawn_main at 0x0000020C2EC0CAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 320
               │     └ 3
               └ <function _main at 0x0000020C2EC0CB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 320
           │    └ <function BaseProcess._bootstrap at 0x0000020C2E923560>
           └ <SpawnProcess name='SpawnProcess-10' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000020C2E8C93A0>
    └ <SpawnProcess name='SpawnProcess-10' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000020C30D3B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-10' parent=20752 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-10' parent=20752 started>
    │    └ <function subprocess_started at 0x0000020C30E3F4C0>
    └ <SpawnProcess name='SpawnProcess-10' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=592, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000020C30D3B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=592, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000020C30E3E3E0>
           │       │   └ <uvicorn.server.Server object at 0x0000020C30D3B380>
           │       └ <function run at 0x0000020C2EC0DD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020C30DF26C0>
           │      └ <function Runner.run at 0x0000020C30B1EF20>
           └ <asyncio.runners.Runner object at 0x0000020C30EA41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020C30B1CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020C30EA41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020C30B1C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020C30B1E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020C30645080>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000020C34D32E40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE350>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000020C3430D010>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000020C34D32E40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE350>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE350>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000020C34D33770>
          └ <fastapi.applications.FastAPI object at 0x0000020C3430D010>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000020C35143B00>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000020C34D33620>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000020C34D33770>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000020C35143B00>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000020C34D334D0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000020C34D33620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000020C35143B00>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000020C350FE210>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000020C34EC3850>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000020C34D334D0>
          └ <function wrap_app_handling_exceptions at 0x0000020C31F2A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020C350DD620>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000020C34EC3850>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020C350DD620>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000020C34EC3850>>
          └ <fastapi.routing.APIRouter object at 0x0000020C34EC3850>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020C350DD620>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x0000020C31F2B920>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020C350DD620>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000020C34FCD080>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020C350DD620>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000020C34E23950>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000020C350DD760>
          └ <function wrap_app_handling_exceptions at 0x0000020C31F2A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020C350DD260>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000020C350FE...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000020C350DD760>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000020C34E23950>
                     └ <function get_request_handler.<locals>.app at 0x0000020C34FCCFE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000020C31F29620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.orm.session.Session object at 0x0000020C350FEFD0>, 'user_login': UserLogin(username='admin', password='123...
                 │         └ <function login at 0x0000020C34325440>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "E:\yingj\emergency-monitoring-system\backend\app\api\v1\auth.py", line 67, in login
    user = auth_service.authenticate_user(
           │            └ <function AuthService.authenticate_user at 0x0000020C34309120>
           └ <app.services.auth_service.AuthService object at 0x0000020C350FED50>

  File "E:\yingj\emergency-monitoring-system\backend\app\services\auth_service.py", line 35, in authenticate_user
    if not verify_password(password, user.hashed_password):
           │               │         │    └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x0000020C33F7E480>
           │               │         └ <app.models.user.User object at 0x0000020C350FF390>
           │               └ '123456'
           └ <function verify_password at 0x0000020C342AA340>

  File "E:\yingj\emergency-monitoring-system\backend\app\core\security.py", line 38, in verify_password
    return pwd_context.verify(plain_password, hashed_password)
           │           │      │               └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │           │      └ '123456'
           │           └ <function CryptContext.verify at 0x0000020C34296480>
           └ <CryptContext at 0x20c341a6f90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\context.py", line 2347, in verify
    return record.verify(secret, hash, **kwds)
           │      │      │       │       └ {}
           │      │      │       └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │      │      └ '123456'
           │      └ <classmethod(<function GenericHandler.verify at 0x0000020C3425BCE0>)>
           └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 792, in verify
    return consteq(self._calc_checksum(secret), chk)
           │       │    │              │        └ 'sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │       │    │              └ '123456'
           │       │    └ <function _NoBackend._calc_checksum at 0x0000020C342A91C0>
           │       └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x0000020C34D33E00>
           └ <built-in function compare_digest>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
    │    └ <classmethod(<function BackendMixin._stub_requires_backend at 0x0000020C3428A020>)>
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x0000020C34D33E00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
    │   └ <classmethod(<function BackendMixin.set_backend at 0x0000020C34289DA0>)>
    └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           │     │           │            └ False
           │     │           └ 'any'
           │     └ <classmethod(<function BackendMixin.set_backend at 0x0000020C34289DA0>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2163, in set_backend
    return cls.set_backend(name, dryrun=dryrun)
           │   │           │            └ False
           │   │           └ 'bcrypt'
           │   └ <classmethod(<function BackendMixin.set_backend at 0x0000020C34289DA0>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2188, in set_backend
    cls._set_backend(name, dryrun)
    │   │            │     └ False
    │   │            └ 'bcrypt'
    │   └ <classmethod(<function SubclassBackendMixin._set_backend at 0x0000020C3428A160>)>
    └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2311, in _set_backend
    super(SubclassBackendMixin, cls)._set_backend(name, dryrun)
          │                     │                 │     └ False
          │                     │                 └ 'bcrypt'
          │                     └ <class 'passlib.handlers.bcrypt.bcrypt'>
          └ <class 'passlib.utils.handlers.SubclassBackendMixin'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2224, in _set_backend
    ok = loader(**kwds)
         │        └ {'name': 'bcrypt', 'dryrun': False}
         └ <bound method _BcryptBackend._load_backend_mixin of <class 'passlib.handlers.bcrypt._BcryptBackend'>>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              └ <module 'bcrypt' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bcrypt\\__i...

AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-03 21:41:30 | DEBUG    | logging:callHandlers:1736 | detected 'bcrypt' backend, version '<unknown>'
2025-08-03 21:41:30 | DEBUG    | logging:callHandlers:1736 | 'bcrypt' backend lacks $2$ support, enabling workaround
2025-08-03 21:41:30 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 21:41:30 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 21:44:19 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 21:44:19 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 21:51:20 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 21:51:20 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 21:54:06 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 21:54:06 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 21:55:56 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 21:55:56 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 21:56:52 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 21:56:52 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 21:58:04 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 21:58:04 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 22:04:02 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 22:04:02 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 22:23:23 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 22:23:23 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 22:24:34 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 22:35:12 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 22:35:14 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 22:35:14 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 22:36:32 | WARNING  | logging:callHandlers:1736 | (trapped) error reading bcrypt version
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=20752, pipe_handle=564)
                                      │           └ <function spawn_main at 0x000001F156A6CAE0>
                                      └ <function spawn_main at 0x000001F156A6CAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 324
               │     └ 3
               └ <function _main at 0x000001F156A6CB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 324
           │    └ <function BaseProcess._bootstrap at 0x000001F156783560>
           └ <SpawnProcess name='SpawnProcess-11' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001F1567293A0>
    └ <SpawnProcess name='SpawnProcess-11' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001F158B4B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-11' parent=20752 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-11' parent=20752 started>
    │    └ <function subprocess_started at 0x000001F158C4F4C0>
    └ <SpawnProcess name='SpawnProcess-11' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=576, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001F158B4B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=576, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001F158C4E3E0>
           │       │   └ <uvicorn.server.Server object at 0x000001F158B4B380>
           │       └ <function run at 0x000001F156A6DD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001F158C026C0>
           │      └ <function Runner.run at 0x000001F15891EF20>
           └ <asyncio.runners.Runner object at 0x000001F158CB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001F15891CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001F158CB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001F15891C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001F15891E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001F156E55080>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001F15CB46E40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08440>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001F15C121010>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001F15CB46E40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08440>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08440>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001F15CB47770>
          └ <fastapi.applications.FastAPI object at 0x000001F15C121010>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001F15CEF14E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001F15CB47620>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001F15CB47770>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001F15CEF14E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001F15CB474D0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001F15CB47620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001F15CEF14E0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001F15CF08590>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001F15CCD3850>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001F15CB474D0>
          └ <function wrap_app_handling_exceptions at 0x000001F159D3A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001F15CEF1620>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001F15CCD3850>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001F15CEF1620>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001F15CCD3850>>
          └ <fastapi.routing.APIRouter object at 0x000001F15CCD3850>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001F15CEF1620>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001F159D3B920>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001F15CEF1620>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001F15CDDD080>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001F15CEF1620>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001F15CF10190>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001F15CEF16C0>
          └ <function wrap_app_handling_exceptions at 0x000001F159D3A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001F15CEF1800>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001F15CF08...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001F15CEF16C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001F15CF10190>
                     └ <function get_request_handler.<locals>.app at 0x000001F15CDDCFE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001F159D39620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.orm.session.Session object at 0x000001F15CF09BE0>, 'user_login': UserLogin(username='admin', password='123...
                 │         └ <function login at 0x000001F15C135440>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "E:\yingj\emergency-monitoring-system\backend\app\api\v1\auth.py", line 67, in login
    user = auth_service.authenticate_user(
           │            └ <function AuthService.authenticate_user at 0x000001F15C119120>
           └ <app.services.auth_service.AuthService object at 0x000001F15CF08C20>

  File "E:\yingj\emergency-monitoring-system\backend\app\services\auth_service.py", line 35, in authenticate_user
    if not verify_password(password, user.hashed_password):
           │               │         │    └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x000001F15BD6E480>
           │               │         └ <app.models.user.User object at 0x000001F15CF0ABA0>
           │               └ '123456'
           └ <function verify_password at 0x000001F15C0BE340>

  File "E:\yingj\emergency-monitoring-system\backend\app\core\security.py", line 38, in verify_password
    return pwd_context.verify(plain_password, hashed_password)
           │           │      │               └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │           │      └ '123456'
           │           └ <function CryptContext.verify at 0x000001F15C0A6480>
           └ <CryptContext at 0x1f15bfb6f90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\context.py", line 2347, in verify
    return record.verify(secret, hash, **kwds)
           │      │      │       │       └ {}
           │      │      │       └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │      │      └ '123456'
           │      └ <classmethod(<function GenericHandler.verify at 0x000001F15C06FCE0>)>
           └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 792, in verify
    return consteq(self._calc_checksum(secret), chk)
           │       │    │              │        └ 'sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │       │    │              └ '123456'
           │       │    └ <function _NoBackend._calc_checksum at 0x000001F15C0BD1C0>
           │       └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000001F15CF0ACF0>
           └ <built-in function compare_digest>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
    │    └ <classmethod(<function BackendMixin._stub_requires_backend at 0x000001F15C09A020>)>
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000001F15CF0ACF0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
    │   └ <classmethod(<function BackendMixin.set_backend at 0x000001F15C099DA0>)>
    └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           │     │           │            └ False
           │     │           └ 'any'
           │     └ <classmethod(<function BackendMixin.set_backend at 0x000001F15C099DA0>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2163, in set_backend
    return cls.set_backend(name, dryrun=dryrun)
           │   │           │            └ False
           │   │           └ 'bcrypt'
           │   └ <classmethod(<function BackendMixin.set_backend at 0x000001F15C099DA0>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2188, in set_backend
    cls._set_backend(name, dryrun)
    │   │            │     └ False
    │   │            └ 'bcrypt'
    │   └ <classmethod(<function SubclassBackendMixin._set_backend at 0x000001F15C09A160>)>
    └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2311, in _set_backend
    super(SubclassBackendMixin, cls)._set_backend(name, dryrun)
          │                     │                 │     └ False
          │                     │                 └ 'bcrypt'
          │                     └ <class 'passlib.handlers.bcrypt.bcrypt'>
          └ <class 'passlib.utils.handlers.SubclassBackendMixin'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2224, in _set_backend
    ok = loader(**kwds)
         │        └ {'name': 'bcrypt', 'dryrun': False}
         └ <bound method _BcryptBackend._load_backend_mixin of <class 'passlib.handlers.bcrypt._BcryptBackend'>>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              └ <module 'bcrypt' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bcrypt\\__i...

AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-03 22:36:32 | DEBUG    | logging:callHandlers:1736 | detected 'bcrypt' backend, version '<unknown>'
2025-08-03 22:36:32 | DEBUG    | logging:callHandlers:1736 | 'bcrypt' backend lacks $2$ support, enabling workaround
2025-08-03 22:36:32 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 22:36:33 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 22:37:16 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 22:37:22 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 22:48:36 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 57793)
2025-08-03 22:48:36 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 57793)
2025-08-03 22:48:36 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 57797)
2025-08-03 22:48:36 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 57797)
2025-08-03 22:49:25 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 22:49:29 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 22:56:16 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 22:56:18 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 22:56:18 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 22:56:36 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 22:56:37 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 22:56:37 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功: 0.0.0.0:9500
2025-08-03 22:56:55 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 22:56:57 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 22:56:57 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 22:56:57 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 22:56:57 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 22:57:25 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 22:57:27 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 22:57:27 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 22:57:27 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 22:57:27 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 22:57:53 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 22:57:54 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 22:57:55 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 22:57:55 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 22:57:55 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 22:58:09 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 22:58:11 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 22:58:11 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 22:58:11 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 22:58:11 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 22:59:22 | WARNING  | logging:callHandlers:1736 | (trapped) error reading bcrypt version
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=20752, pipe_handle=564)
                                      │           └ <function spawn_main at 0x000001C7BD47CAE0>
                                      └ <function spawn_main at 0x000001C7BD47CAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 324
               │     └ 3
               └ <function _main at 0x000001C7BD47CB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 324
           │    └ <function BaseProcess._bootstrap at 0x000001C7BD193560>
           └ <SpawnProcess name='SpawnProcess-17' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001C7BD1393A0>
    └ <SpawnProcess name='SpawnProcess-17' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001C7BF54B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-17' parent=20752 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-17' parent=20752 started>
    │    └ <function subprocess_started at 0x000001C7BF64F4C0>
    └ <SpawnProcess name='SpawnProcess-17' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=572, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001C7BF54B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=572, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001C7BF64E3E0>
           │       │   └ <uvicorn.server.Server object at 0x000001C7BF54B380>
           │       └ <function run at 0x000001C7BD47DD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001C7BF6026C0>
           │      └ <function Runner.run at 0x000001C7BF31EF20>
           └ <asyncio.runners.Runner object at 0x000001C7BF6B41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001C7BF31CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001C7BF6B41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001C7BF31C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001C7BF31E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001C7BEE45080>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001C7C3542E40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C440>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001C7C2B21010>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001C7C3542E40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C440>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C440>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001C7C3543770>
          └ <fastapi.applications.FastAPI object at 0x000001C7C2B21010>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001C7C38FD620>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001C7C3543620>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001C7C3543770>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001C7C38FD620>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001C7C35434D0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001C7C3543620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001C7C38FD620>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001C7C390C590>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001C7C369B850>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001C7C35434D0>
          └ <function wrap_app_handling_exceptions at 0x000001C7C073A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001C7C38FD760>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001C7C369B850>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001C7C38FD760>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001C7C369B850>>
          └ <fastapi.routing.APIRouter object at 0x000001C7C369B850>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001C7C38FD760>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001C7C073B920>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001C7C38FD760>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001C7C37D51C0>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001C7C38FD760>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001C7C3908410>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001C7C38FD800>
          └ <function wrap_app_handling_exceptions at 0x000001C7C073A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001C7C38FD940>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C7C390C...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001C7C38FD800>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001C7C3908410>
                     └ <function get_request_handler.<locals>.app at 0x000001C7C37D5120>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001C7C0739620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.orm.session.Session object at 0x000001C7C390DBE0>, 'user_login': UserLogin(username='admin', password='123...
                 │         └ <function login at 0x000001C7C2B35440>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "E:\yingj\emergency-monitoring-system\backend\app\api\v1\auth.py", line 67, in login
    user = auth_service.authenticate_user(
           │            └ <function AuthService.authenticate_user at 0x000001C7C2B15120>
           └ <app.services.auth_service.AuthService object at 0x000001C7C390CC20>

  File "E:\yingj\emergency-monitoring-system\backend\app\services\auth_service.py", line 35, in authenticate_user
    if not verify_password(password, user.hashed_password):
           │               │         │    └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x000001C7C278E480>
           │               │         └ <app.models.user.User object at 0x000001C7C390EBA0>
           │               └ '123456'
           └ <function verify_password at 0x000001C7C2ABA340>

  File "E:\yingj\emergency-monitoring-system\backend\app\core\security.py", line 38, in verify_password
    return pwd_context.verify(plain_password, hashed_password)
           │           │      │               └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │           │      └ '123456'
           │           └ <function CryptContext.verify at 0x000001C7C2AA6480>
           └ <CryptContext at 0x1c7c29b6f90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\context.py", line 2347, in verify
    return record.verify(secret, hash, **kwds)
           │      │      │       │       └ {}
           │      │      │       └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │      │      └ '123456'
           │      └ <classmethod(<function GenericHandler.verify at 0x000001C7C2A6BCE0>)>
           └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 792, in verify
    return consteq(self._calc_checksum(secret), chk)
           │       │    │              │        └ 'sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │       │    │              └ '123456'
           │       │    └ <function _NoBackend._calc_checksum at 0x000001C7C2AB91C0>
           │       └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000001C7C390ECF0>
           └ <built-in function compare_digest>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
    │    └ <classmethod(<function BackendMixin._stub_requires_backend at 0x000001C7C2A9A020>)>
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000001C7C390ECF0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
    │   └ <classmethod(<function BackendMixin.set_backend at 0x000001C7C2A99DA0>)>
    └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           │     │           │            └ False
           │     │           └ 'any'
           │     └ <classmethod(<function BackendMixin.set_backend at 0x000001C7C2A99DA0>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2163, in set_backend
    return cls.set_backend(name, dryrun=dryrun)
           │   │           │            └ False
           │   │           └ 'bcrypt'
           │   └ <classmethod(<function BackendMixin.set_backend at 0x000001C7C2A99DA0>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2188, in set_backend
    cls._set_backend(name, dryrun)
    │   │            │     └ False
    │   │            └ 'bcrypt'
    │   └ <classmethod(<function SubclassBackendMixin._set_backend at 0x000001C7C2A9A160>)>
    └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2311, in _set_backend
    super(SubclassBackendMixin, cls)._set_backend(name, dryrun)
          │                     │                 │     └ False
          │                     │                 └ 'bcrypt'
          │                     └ <class 'passlib.handlers.bcrypt.bcrypt'>
          └ <class 'passlib.utils.handlers.SubclassBackendMixin'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2224, in _set_backend
    ok = loader(**kwds)
         │        └ {'name': 'bcrypt', 'dryrun': False}
         └ <bound method _BcryptBackend._load_backend_mixin of <class 'passlib.handlers.bcrypt._BcryptBackend'>>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              └ <module 'bcrypt' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bcrypt\\__i...

AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-03 22:59:22 | DEBUG    | logging:callHandlers:1736 | detected 'bcrypt' backend, version '<unknown>'
2025-08-03 22:59:22 | DEBUG    | logging:callHandlers:1736 | 'bcrypt' backend lacks $2$ support, enabling workaround
2025-08-03 22:59:22 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 22:59:22 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 22:59:26 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 22:59:28 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 22:59:28 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 22:59:28 | INFO     | logging:callHandlers:1736 |   公网地址: **************:9500
2025-08-03 22:59:29 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58177)
2025-08-03 22:59:29 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58177)
2025-08-03 23:02:24 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:02:26 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:02:26 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:02:26 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:02:26 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:02:39 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58325)
2025-08-03 23:02:39 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58325)
2025-08-03 23:02:51 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58335)
2025-08-03 23:02:51 | INFO     | logging:callHandlers:1736 | 摄像头注册成功: ID=12345, IP=*************:554
2025-08-03 23:03:21 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58335)
2025-08-03 23:03:32 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58351)
2025-08-03 23:03:32 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58351)
2025-08-03 23:03:32 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58356)
2025-08-03 23:03:32 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58356)
2025-08-03 23:03:33 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58357)
2025-08-03 23:03:33 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58357)
2025-08-03 23:03:51 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:03:53 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:03:53 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:03:53 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:03:53 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:04:05 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58396)
2025-08-03 23:04:05 | INFO     | logging:callHandlers:1736 | 摄像头注册成功: ID=10001, IP=*************:554
2025-08-03 23:04:06 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58397)
2025-08-03 23:04:06 | INFO     | logging:callHandlers:1736 | 摄像头注册成功: ID=10002, IP=*************:554
2025-08-03 23:04:06 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58399)
2025-08-03 23:04:06 | INFO     | logging:callHandlers:1736 | 摄像头注册成功: ID=10003, IP=*************:554
2025-08-03 23:04:20 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58396)
2025-08-03 23:04:21 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58397)
2025-08-03 23:04:21 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58399)
2025-08-03 23:04:44 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:11:41 | WARNING  | logging:callHandlers:1736 | (trapped) error reading bcrypt version
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=20752, pipe_handle=564)
                                      │           └ <function spawn_main at 0x000001AA829CCAE0>
                                      └ <function spawn_main at 0x000001AA829CCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 312
               │     └ 3
               └ <function _main at 0x000001AA829CCB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 312
           │    └ <function BaseProcess._bootstrap at 0x000001AA826E3560>
           └ <SpawnProcess name='SpawnProcess-19' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001AA826893A0>
    └ <SpawnProcess name='SpawnProcess-19' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001AA84ADB230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-19' parent=20752 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-19' parent=20752 started>
    │    └ <function subprocess_started at 0x000001AA84BCF4C0>
    └ <SpawnProcess name='SpawnProcess-19' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=568, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001AA84ADB380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=568, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001AA84BCE3E0>
           │       │   └ <uvicorn.server.Server object at 0x000001AA84ADB380>
           │       └ <function run at 0x000001AA829CDD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001AA84B926C0>
           │      └ <function Runner.run at 0x000001AA848CEF20>
           └ <asyncio.runners.Runner object at 0x000001AA84C341A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001AA848CCA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001AA84C341A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001AA848CC9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001AA848CE7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001AA843F5080>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001AA88ACF0E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E430>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001AA88111010>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001AA88ACF0E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E430>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E430>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001AA88ACFA10>
          └ <fastapi.applications.FastAPI object at 0x000001AA88111010>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001AA88F07E20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001AA88ACF8C0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001AA88ACFA10>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001AA88F07E20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001AA88ACF770>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001AA88ACF8C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001AA88F07E20>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001AA88F07D90>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001AA88C63950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001AA88ACF770>
          └ <function wrap_app_handling_exceptions at 0x000001AA85CBA020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001AA89061080>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001AA88C63950>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001AA89061080>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001AA88C63950>>
          └ <fastapi.routing.APIRouter object at 0x000001AA88C63950>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001AA89061080>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001AA85CBB920>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001AA89061080>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001AA88D651C0>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001AA89061080>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001AA89060B90>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001AA89060AE0>
          └ <function wrap_app_handling_exceptions at 0x000001AA85CBA020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001AA89060CC0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001AA8904E...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001AA89060AE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001AA89060B90>
                     └ <function get_request_handler.<locals>.app at 0x000001AA88D65120>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001AA85CB9620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.orm.session.Session object at 0x000001AA891A8FC0>, 'user_login': UserLogin(username='admin', password='123...
                 │         └ <function login at 0x000001AA88125440>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "E:\yingj\emergency-monitoring-system\backend\app\api\v1\auth.py", line 67, in login
    user = auth_service.authenticate_user(
           │            └ <function AuthService.authenticate_user at 0x000001AA88109120>
           └ <app.services.auth_service.AuthService object at 0x000001AA88E95F90>

  File "E:\yingj\emergency-monitoring-system\backend\app\services\auth_service.py", line 35, in authenticate_user
    if not verify_password(password, user.hashed_password):
           │               │         │    └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x000001AA87D7E480>
           │               │         └ <app.models.user.User object at 0x000001AA891A8D60>
           │               └ '123456'
           └ <function verify_password at 0x000001AA880AE340>

  File "E:\yingj\emergency-monitoring-system\backend\app\core\security.py", line 38, in verify_password
    return pwd_context.verify(plain_password, hashed_password)
           │           │      │               └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │           │      └ '123456'
           │           └ <function CryptContext.verify at 0x000001AA88096480>
           └ <CryptContext at 0x1aa87fa6f90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\context.py", line 2347, in verify
    return record.verify(secret, hash, **kwds)
           │      │      │       │       └ {}
           │      │      │       └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │      │      └ '123456'
           │      └ <classmethod(<function GenericHandler.verify at 0x000001AA8805BCE0>)>
           └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 792, in verify
    return consteq(self._calc_checksum(secret), chk)
           │       │    │              │        └ 'sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │       │    │              └ '123456'
           │       │    └ <function _NoBackend._calc_checksum at 0x000001AA880AD1C0>
           │       └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000001AA88E9ECF0>
           └ <built-in function compare_digest>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
    │    └ <classmethod(<function BackendMixin._stub_requires_backend at 0x000001AA8808A020>)>
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000001AA88E9ECF0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
    │   └ <classmethod(<function BackendMixin.set_backend at 0x000001AA88089DA0>)>
    └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           │     │           │            └ False
           │     │           └ 'any'
           │     └ <classmethod(<function BackendMixin.set_backend at 0x000001AA88089DA0>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2163, in set_backend
    return cls.set_backend(name, dryrun=dryrun)
           │   │           │            └ False
           │   │           └ 'bcrypt'
           │   └ <classmethod(<function BackendMixin.set_backend at 0x000001AA88089DA0>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2188, in set_backend
    cls._set_backend(name, dryrun)
    │   │            │     └ False
    │   │            └ 'bcrypt'
    │   └ <classmethod(<function SubclassBackendMixin._set_backend at 0x000001AA8808A160>)>
    └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2311, in _set_backend
    super(SubclassBackendMixin, cls)._set_backend(name, dryrun)
          │                     │                 │     └ False
          │                     │                 └ 'bcrypt'
          │                     └ <class 'passlib.handlers.bcrypt.bcrypt'>
          └ <class 'passlib.utils.handlers.SubclassBackendMixin'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2224, in _set_backend
    ok = loader(**kwds)
         │        └ {'name': 'bcrypt', 'dryrun': False}
         └ <bound method _BcryptBackend._load_backend_mixin of <class 'passlib.handlers.bcrypt._BcryptBackend'>>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              └ <module 'bcrypt' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bcrypt\\__i...

AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-03 23:11:41 | DEBUG    | logging:callHandlers:1736 | detected 'bcrypt' backend, version '<unknown>'
2025-08-03 23:11:41 | DEBUG    | logging:callHandlers:1736 | 'bcrypt' backend lacks $2$ support, enabling workaround
2025-08-03 23:11:42 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-03 23:11:42 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-03 23:11:46 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:11:46 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:11:46 | INFO     | logging:callHandlers:1736 |   公网地址: **************:9500
2025-08-03 23:11:46 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 10001
2025-08-03 23:11:46 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 10002
2025-08-03 23:11:46 | WARNING  | logging:callHandlers:1736 | 摄像头心跳超时: 10003
2025-08-03 23:11:51 | INFO     | logging:callHandlers:1736 | 摄像头连接: ('127.0.0.1', 58720)
2025-08-03 23:11:51 | INFO     | logging:callHandlers:1736 | 摄像头断开连接: ('127.0.0.1', 58720)
2025-08-03 23:11:55 | INFO     | app.services.device_service:create_device:64 | 设备创建成功: 99999
2025-08-03 23:11:57 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:19:34 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:19:36 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:19:36 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:19:36 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:19:36 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:19:52 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:19:54 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:19:54 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:19:54 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:19:54 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:20:34 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:20:36 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:20:36 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:20:36 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:20:36 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:20:54 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:20:56 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:20:56 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:20:56 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:20:56 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:21:51 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:21:53 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:21:53 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:21:53 | INFO     | logging:callHandlers:1736 |   公网地址: **************:9500
2025-08-03 23:21:56 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:21:58 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:21:58 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:21:58 | INFO     | logging:callHandlers:1736 |   公网地址: 127.0.0.1:9500
2025-08-03 23:22:01 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:22:03 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:22:03 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:22:03 | INFO     | logging:callHandlers:1736 |   公网地址: 127.0.0.1:9500
2025-08-03 23:22:06 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:22:09 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:22:09 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:22:09 | INFO     | logging:callHandlers:1736 |   公网地址: **************:9500
2025-08-03 23:22:52 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:25:50 | ERROR    | logging:callHandlers:1736 | 启动注册服务器失败: could not bind on any address out of [('**************', 9500)]
2025-08-03 23:25:50 | ERROR    | logging:callHandlers:1736 | Task exception was never retrieved
future: <Task finished name='Task-69' coro=<CameraRegistrationServer.start_server() done, defined at E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py:70> exception=OSError("could not bind on any address out of [('**************', 9500)]")>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=20752, pipe_handle=564)
                                      │           └ <function spawn_main at 0x00000203827BCAE0>
                                      └ <function spawn_main at 0x00000203827BCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 296
               │     └ 3
               └ <function _main at 0x00000203827BCB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 296
           │    └ <function BaseProcess._bootstrap at 0x00000203824D3560>
           └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000203824793A0>
    └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002038495B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
    │    └ <function subprocess_started at 0x0000020384A4F4C0>
    └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=588, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002038495B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=588, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000020384A4E3E0>
           │       │   └ <uvicorn.server.Server object at 0x000002038495B380>
           │       └ <function run at 0x00000203827BDD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020384A126C0>
           │      └ <function Runner.run at 0x000002038474EF20>
           └ <asyncio.runners.Runner object at 0x0000020384AB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002038474CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020384AB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002038474C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002038474E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2041, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.

> File "E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py", line 73, in start_server
    self.server = await asyncio.start_server(
    │    │              │       └ <function start_server at 0x000002038474F420>
    │    │              └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
    │    └ <Server sockets=()>
    └ <app.services.camera_registration_server.CameraRegistrationServer object at 0x00000203888C1400>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\streams.py", line 84, in start_server
    return await loop.create_server(factory, host, port, **kwds)
                 │    │             │        │     │       └ {}
                 │    │             │        │     └ 9500
                 │    │             │        └ '**************'
                 │    │             └ <function start_server.<locals>.factory at 0x0000020388FC6A20>
                 │    └ <function BaseEventLoop.create_server at 0x000002038474DEE0>
                 └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 1625, in create_server
    raise OSError('could not bind on any address out of %r'

OSError: could not bind on any address out of [('**************', 9500)]
2025-08-03 23:28:18 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:28:18 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:28:18 | INFO     | logging:callHandlers:1736 |   公网地址: **************:9500
2025-08-03 23:30:12 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:30:30 | ERROR    | logging:callHandlers:1736 | 启动注册服务器失败: could not bind on any address out of [('**************', 9500)]
2025-08-03 23:30:30 | ERROR    | logging:callHandlers:1736 | Task exception was never retrieved
future: <Task finished name='Task-126' coro=<CameraRegistrationServer.start_server() done, defined at E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py:70> exception=OSError("could not bind on any address out of [('**************', 9500)]")>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=20752, pipe_handle=564)
                                      │           └ <function spawn_main at 0x00000203827BCAE0>
                                      └ <function spawn_main at 0x00000203827BCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 296
               │     └ 3
               └ <function _main at 0x00000203827BCB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 296
           │    └ <function BaseProcess._bootstrap at 0x00000203824D3560>
           └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000203824793A0>
    └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002038495B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
    │    └ <function subprocess_started at 0x0000020384A4F4C0>
    └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=588, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002038495B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=588, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000020384A4E3E0>
           │       │   └ <uvicorn.server.Server object at 0x000002038495B380>
           │       └ <function run at 0x00000203827BDD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020384A126C0>
           │      └ <function Runner.run at 0x000002038474EF20>
           └ <asyncio.runners.Runner object at 0x0000020384AB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002038474CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020384AB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002038474C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002038474E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2041, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.

> File "E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py", line 73, in start_server
    self.server = await asyncio.start_server(
    │    │              │       └ <function start_server at 0x000002038474F420>
    │    │              └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
    │    └ <Server sockets=()>
    └ <app.services.camera_registration_server.CameraRegistrationServer object at 0x00000203888C1400>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\streams.py", line 84, in start_server
    return await loop.create_server(factory, host, port, **kwds)
                 │    │             │        │     │       └ {}
                 │    │             │        │     └ 9500
                 │    │             │        └ '**************'
                 │    │             └ <function start_server.<locals>.factory at 0x0000020388FC67A0>
                 │    └ <function BaseEventLoop.create_server at 0x000002038474DEE0>
                 └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 1625, in create_server
    raise OSError('could not bind on any address out of %r'

OSError: could not bind on any address out of [('**************', 9500)]
2025-08-03 23:30:32 | ERROR    | logging:callHandlers:1736 | 启动注册服务器失败: could not bind on any address out of [('**************', 9500)]
2025-08-03 23:30:32 | ERROR    | logging:callHandlers:1736 | Task exception was never retrieved
future: <Task finished name='Task-130' coro=<CameraRegistrationServer.start_server() done, defined at E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py:70> exception=OSError("could not bind on any address out of [('**************', 9500)]")>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=20752, pipe_handle=564)
                                      │           └ <function spawn_main at 0x00000203827BCAE0>
                                      └ <function spawn_main at 0x00000203827BCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 296
               │     └ 3
               └ <function _main at 0x00000203827BCB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 296
           │    └ <function BaseProcess._bootstrap at 0x00000203824D3560>
           └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000203824793A0>
    └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002038495B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
    │    └ <function subprocess_started at 0x0000020384A4F4C0>
    └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=588, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002038495B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=588, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000020384A4E3E0>
           │       │   └ <uvicorn.server.Server object at 0x000002038495B380>
           │       └ <function run at 0x00000203827BDD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020384A126C0>
           │      └ <function Runner.run at 0x000002038474EF20>
           └ <asyncio.runners.Runner object at 0x0000020384AB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002038474CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020384AB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002038474C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002038474E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2041, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.

> File "E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py", line 73, in start_server
    self.server = await asyncio.start_server(
    │    │              │       └ <function start_server at 0x000002038474F420>
    │    │              └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
    │    └ <Server sockets=()>
    └ <app.services.camera_registration_server.CameraRegistrationServer object at 0x00000203888C1400>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\streams.py", line 84, in start_server
    return await loop.create_server(factory, host, port, **kwds)
                 │    │             │        │     │       └ {}
                 │    │             │        │     └ 9500
                 │    │             │        └ '**************'
                 │    │             └ <function start_server.<locals>.factory at 0x0000020388FC7A60>
                 │    └ <function BaseEventLoop.create_server at 0x000002038474DEE0>
                 └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 1625, in create_server
    raise OSError('could not bind on any address out of %r'

OSError: could not bind on any address out of [('**************', 9500)]
2025-08-03 23:35:38 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:35:38 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:35:38 | INFO     | logging:callHandlers:1736 |   公网地址: **************:9500
2025-08-03 23:36:36 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:36:45 | ERROR    | logging:callHandlers:1736 | 启动注册服务器失败: could not bind on any address out of [('**************', 9500)]
2025-08-03 23:36:45 | ERROR    | logging:callHandlers:1736 | Task exception was never retrieved
future: <Task finished name='Task-171' coro=<CameraRegistrationServer.start_server() done, defined at E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py:70> exception=OSError("could not bind on any address out of [('**************', 9500)]")>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=20752, pipe_handle=564)
                                      │           └ <function spawn_main at 0x00000203827BCAE0>
                                      └ <function spawn_main at 0x00000203827BCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 296
               │     └ 3
               └ <function _main at 0x00000203827BCB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 296
           │    └ <function BaseProcess._bootstrap at 0x00000203824D3560>
           └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000203824793A0>
    └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002038495B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
    │    └ <function subprocess_started at 0x0000020384A4F4C0>
    └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=588, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002038495B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=588, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000020384A4E3E0>
           │       │   └ <uvicorn.server.Server object at 0x000002038495B380>
           │       └ <function run at 0x00000203827BDD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020384A126C0>
           │      └ <function Runner.run at 0x000002038474EF20>
           └ <asyncio.runners.Runner object at 0x0000020384AB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002038474CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020384AB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002038474C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002038474E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2041, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.

> File "E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py", line 73, in start_server
    self.server = await asyncio.start_server(
    │    │              │       └ <function start_server at 0x000002038474F420>
    │    │              └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
    │    └ <Server sockets=()>
    └ <app.services.camera_registration_server.CameraRegistrationServer object at 0x00000203888C1400>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\streams.py", line 84, in start_server
    return await loop.create_server(factory, host, port, **kwds)
                 │    │             │        │     │       └ {}
                 │    │             │        │     └ 9500
                 │    │             │        └ '**************'
                 │    │             └ <function start_server.<locals>.factory at 0x0000020388FC7CE0>
                 │    └ <function BaseEventLoop.create_server at 0x000002038474DEE0>
                 └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 1625, in create_server
    raise OSError('could not bind on any address out of %r'

OSError: could not bind on any address out of [('**************', 9500)]
2025-08-03 23:40:55 | ERROR    | logging:callHandlers:1736 | 启动注册服务器失败: could not bind on any address out of [('**************', 9500)]
2025-08-03 23:40:55 | ERROR    | logging:callHandlers:1736 | Task exception was never retrieved
future: <Task finished name='Task-261' coro=<CameraRegistrationServer.start_server() done, defined at E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py:70> exception=OSError("could not bind on any address out of [('**************', 9500)]")>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=20752, pipe_handle=564)
                                      │           └ <function spawn_main at 0x00000203827BCAE0>
                                      └ <function spawn_main at 0x00000203827BCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 296
               │     └ 3
               └ <function _main at 0x00000203827BCB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 296
           │    └ <function BaseProcess._bootstrap at 0x00000203824D3560>
           └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000203824793A0>
    └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002038495B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
    │    └ <function subprocess_started at 0x0000020384A4F4C0>
    └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=588, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002038495B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=588, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000020384A4E3E0>
           │       │   └ <uvicorn.server.Server object at 0x000002038495B380>
           │       └ <function run at 0x00000203827BDD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020384A126C0>
           │      └ <function Runner.run at 0x000002038474EF20>
           └ <asyncio.runners.Runner object at 0x0000020384AB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002038474CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020384AB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002038474C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002038474E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2041, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.

> File "E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py", line 73, in start_server
    self.server = await asyncio.start_server(
    │    │              │       └ <function start_server at 0x000002038474F420>
    │    │              └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
    │    └ <Server sockets=()>
    └ <app.services.camera_registration_server.CameraRegistrationServer object at 0x00000203888C1400>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\streams.py", line 84, in start_server
    return await loop.create_server(factory, host, port, **kwds)
                 │    │             │        │     │       └ {}
                 │    │             │        │     └ 9500
                 │    │             │        └ '**************'
                 │    │             └ <function start_server.<locals>.factory at 0x0000020388FC7920>
                 │    └ <function BaseEventLoop.create_server at 0x000002038474DEE0>
                 └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 1625, in create_server
    raise OSError('could not bind on any address out of %r'

OSError: could not bind on any address out of [('**************', 9500)]
2025-08-03 23:41:15 | ERROR    | logging:callHandlers:1736 | 启动注册服务器失败: could not bind on any address out of [('**************', 9500)]
2025-08-03 23:41:15 | ERROR    | logging:callHandlers:1736 | Task exception was never retrieved
future: <Task finished name='Task-275' coro=<CameraRegistrationServer.start_server() done, defined at E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py:70> exception=OSError("could not bind on any address out of [('**************', 9500)]")>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=20752, pipe_handle=564)
                                      │           └ <function spawn_main at 0x00000203827BCAE0>
                                      └ <function spawn_main at 0x00000203827BCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 296
               │     └ 3
               └ <function _main at 0x00000203827BCB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 296
           │    └ <function BaseProcess._bootstrap at 0x00000203824D3560>
           └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000203824793A0>
    └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002038495B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
    │    └ <function subprocess_started at 0x0000020384A4F4C0>
    └ <SpawnProcess name='SpawnProcess-23' parent=20752 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=588, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002038495B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=588, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000020384A4E3E0>
           │       │   └ <uvicorn.server.Server object at 0x000002038495B380>
           │       └ <function run at 0x00000203827BDD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020384A126C0>
           │      └ <function Runner.run at 0x000002038474EF20>
           └ <asyncio.runners.Runner object at 0x0000020384AB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002038474CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020384AB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002038474C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002038474E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2041, in _run_once
    handle = None  # Needed to break cycles when an exception occurs.
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 105, in _run
    self = None  # Needed to break cycles when an exception occurs.

> File "E:\yingj\emergency-monitoring-system\backend\app\services\camera_registration_server.py", line 73, in start_server
    self.server = await asyncio.start_server(
    │    │              │       └ <function start_server at 0x000002038474F420>
    │    │              └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
    │    └ <Server sockets=()>
    └ <app.services.camera_registration_server.CameraRegistrationServer object at 0x00000203888C1400>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\streams.py", line 84, in start_server
    return await loop.create_server(factory, host, port, **kwds)
                 │    │             │        │     │       └ {}
                 │    │             │        │     └ 9500
                 │    │             │        └ '**************'
                 │    │             └ <function start_server.<locals>.factory at 0x0000020388FC6520>
                 │    └ <function BaseEventLoop.create_server at 0x000002038474DEE0>
                 └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 1625, in create_server
    raise OSError('could not bind on any address out of %r'

OSError: could not bind on any address out of [('**************', 9500)]
2025-08-03 23:42:17 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:42:19 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:42:19 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:42:19 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:42:19 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:42:34 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:42:39 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:42:39 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:42:39 | INFO     | logging:callHandlers:1736 |   公网地址: **************:9500
2025-08-03 23:51:39 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:51:40 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:51:41 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:51:41 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:51:41 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:52:48 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:52:48 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:52:48 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:52:48 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:52:59 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:54:43 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:54:45 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:54:45 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:54:45 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:54:45 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:55:01 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:55:03 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:55:03 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:55:03 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:55:03 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:55:16 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:55:18 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:55:18 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:55:18 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:55:18 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:55:32 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:55:34 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:55:34 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:55:34 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:55:34 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:55:48 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:55:49 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:55:50 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:55:50 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:55:50 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:56:22 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:56:24 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:56:24 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:56:24 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:56:24 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-03 23:56:28 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:56:38 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-03 23:56:40 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-03 23:56:40 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-03 23:56:40 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-03 23:56:40 | INFO     | logging:callHandlers:1736 |   公网地址: ************:9500
2025-08-04 00:00:20 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:00:22 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:02:02 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:02:04 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:02:18 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:02:20 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:03:08 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:04:45 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:05:14 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-04 00:05:14 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-04 00:05:14 | INFO     | logging:callHandlers:1736 |   公网地址: 127.0.0.1:9500
2025-08-04 00:05:35 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:06:05 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-04 00:06:05 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-04 00:06:05 | INFO     | logging:callHandlers:1736 |   公网地址: 127.0.0.1:9500
2025-08-04 00:06:38 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:10:05 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:10:56 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-04 00:10:56 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-04 00:10:56 | INFO     | logging:callHandlers:1736 |   公网地址: 127.0.0.1:9500
2025-08-04 00:13:07 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:14:22 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:14:24 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:15:20 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-04 00:15:20 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-04 00:15:20 | INFO     | logging:callHandlers:1736 |   公网地址: 127.0.0.1:9500
2025-08-04 00:16:24 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:16:26 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:16:47 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:16:48 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:18:40 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:18:42 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:19:03 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:19:05 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:20:55 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:20:57 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:21:17 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:21:18 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:22:15 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:22:17 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:23:16 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-04 00:23:16 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-04 00:23:16 | INFO     | logging:callHandlers:1736 |   公网地址: **************:9500
2025-08-04 00:25:06 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:30:03 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:30:39 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:30:41 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:33:33 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:33:35 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:34:10 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:34:12 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:35:37 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:35:39 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:35:55 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:35:57 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:37:37 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:37:38 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:40:59 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:41:01 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:41:27 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:41:29 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:44:11 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:44:13 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:51:37 | WARNING  | logging:callHandlers:1736 | (trapped) error reading bcrypt version
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=9584, pipe_handle=560)
                                      │           └ <function spawn_main at 0x0000024DC1ACCAE0>
                                      └ <function spawn_main at 0x0000024DC1ACCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 316
               │     └ 3
               └ <function _main at 0x0000024DC1ACCB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 316
           │    └ <function BaseProcess._bootstrap at 0x0000024DC17E3560>
           └ <SpawnProcess name='SpawnProcess-10' parent=9584 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000024DC178D3A0>
    └ <SpawnProcess name='SpawnProcess-10' parent=9584 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000024DC3BAB230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-10' parent=9584 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-10' parent=9584 started>
    │    └ <function subprocess_started at 0x0000024DC3CAF4C0>
    └ <SpawnProcess name='SpawnProcess-10' parent=9584 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=564, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000024DC3BAB380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=564, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000024DC3CAE3E0>
           │       │   └ <uvicorn.server.Server object at 0x0000024DC3BAB380>
           │       └ <function run at 0x0000024DC1ACDD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000024DC3C627A0>
           │      └ <function Runner.run at 0x0000024DC3952F20>
           └ <asyncio.runners.Runner object at 0x0000024DC3D141A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000024DC3950A40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000024DC3D141A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000024DC39509A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000024DC39527A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000024DC3475080>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000024DC7B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47CB0>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000024DC71996A0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000024DC7B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47CB0>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47CB0>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000024DC7F98050>
          └ <fastapi.applications.FastAPI object at 0x0000024DC71996A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000024DC7E50E00>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000024DC7B93E00>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000024DC7F98050>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '60', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000024DC7E50E00>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000024DC4E07CE0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000024DC7B93E00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000024DC7B93E00...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000024DC7B93CB0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000024DC7B93E00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000024DC7B93E00...
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000024DC80064C0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000024DC6D07B60>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000024DC7B93CB0>
          └ <function wrap_app_handling_exceptions at 0x0000024DC4D967A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000024DC7F9C220>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000024DC6D07B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000024DC7F9C220>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000024DC6D07B60>>
          └ <fastapi.routing.APIRouter object at 0x0000024DC6D07B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000024DC7F9C220>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x0000024DC4DC00E0>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000024DC7F9C220>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000024DC7E531A0>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000024DC7F9C220>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000024DC8006150>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000024DC7F9C040>
          └ <function wrap_app_handling_exceptions at 0x0000024DC4D967A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000024DC8156FC0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000024DC7F47...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000024DC7F9C040>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000024DC8006150>
                     └ <function get_request_handler.<locals>.app at 0x0000024DC7E53100>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000024DC4D95DA0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.orm.session.Session object at 0x0000024DC7FB57B0>, 'user_login': UserLogin(username='admin', password='123...
                 │         └ <function login at 0x0000024DC71C5B20>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "E:\yingj\emergency-monitoring-system\backend\app\api\v1\auth.py", line 67, in login
    user = auth_service.authenticate_user(
           │            └ <function AuthService.authenticate_user at 0x0000024DC71AD800>
           └ <app.services.auth_service.AuthService object at 0x0000024DC7FA0050>

  File "E:\yingj\emergency-monitoring-system\backend\app\services\auth_service.py", line 35, in authenticate_user
    if not verify_password(password, user.hashed_password):
           │               │         │    └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x0000024DC6DFEB60>
           │               │         └ <app.models.user.User object at 0x0000024DC7EA78A0>
           │               └ '123456'
           └ <function verify_password at 0x0000024DC7146A20>

  File "E:\yingj\emergency-monitoring-system\backend\app\core\security.py", line 38, in verify_password
    return pwd_context.verify(plain_password, hashed_password)
           │           │      │               └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │           │      └ '123456'
           │           └ <function CryptContext.verify at 0x0000024DC713AB60>
           └ <CryptContext at 0x24dc704b4d0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\context.py", line 2347, in verify
    return record.verify(secret, hash, **kwds)
           │      │      │       │       └ {}
           │      │      │       └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │      │      └ '123456'
           │      └ <classmethod(<function GenericHandler.verify at 0x0000024DC712C400>)>
           └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 792, in verify
    return consteq(self._calc_checksum(secret), chk)
           │       │    │              │        └ 'sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │       │    │              └ '123456'
           │       │    └ <function _NoBackend._calc_checksum at 0x0000024DC71458A0>
           │       └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x0000024DC7F997F0>
           └ <built-in function compare_digest>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
    │    └ <classmethod(<function BackendMixin._stub_requires_backend at 0x0000024DC712E700>)>
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x0000024DC7F997F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
    │   └ <classmethod(<function BackendMixin.set_backend at 0x0000024DC712E480>)>
    └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           │     │           │            └ False
           │     │           └ 'any'
           │     └ <classmethod(<function BackendMixin.set_backend at 0x0000024DC712E480>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2163, in set_backend
    return cls.set_backend(name, dryrun=dryrun)
           │   │           │            └ False
           │   │           └ 'bcrypt'
           │   └ <classmethod(<function BackendMixin.set_backend at 0x0000024DC712E480>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2188, in set_backend
    cls._set_backend(name, dryrun)
    │   │            │     └ False
    │   │            └ 'bcrypt'
    │   └ <classmethod(<function SubclassBackendMixin._set_backend at 0x0000024DC712E840>)>
    └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2311, in _set_backend
    super(SubclassBackendMixin, cls)._set_backend(name, dryrun)
          │                     │                 │     └ False
          │                     │                 └ 'bcrypt'
          │                     └ <class 'passlib.handlers.bcrypt.bcrypt'>
          └ <class 'passlib.utils.handlers.SubclassBackendMixin'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2224, in _set_backend
    ok = loader(**kwds)
         │        └ {'name': 'bcrypt', 'dryrun': False}
         └ <bound method _BcryptBackend._load_backend_mixin of <class 'passlib.handlers.bcrypt._BcryptBackend'>>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              └ <module 'bcrypt' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bcrypt\\__i...

AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-04 00:51:37 | DEBUG    | logging:callHandlers:1736 | detected 'bcrypt' backend, version '<unknown>'
2025-08-04 00:51:37 | DEBUG    | logging:callHandlers:1736 | 'bcrypt' backend lacks $2$ support, enabling workaround
2025-08-04 00:51:38 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-04 00:51:38 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-04 00:53:12 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 00:53:14 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 00:53:48 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-04 00:53:48 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-04 00:53:48 | INFO     | logging:callHandlers:1736 |   公网地址: **************:9500
2025-08-04 00:54:04 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 13:02:56 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 13:06:16 | WARNING  | logging:callHandlers:1736 | (trapped) error reading bcrypt version
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=2832, pipe_handle=556)
                                      │           └ <function spawn_main at 0x000001DD0A9BCAE0>
                                      └ <function spawn_main at 0x000001DD0A9BCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 320
               │     └ 3
               └ <function _main at 0x000001DD0A9BCB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 320
           │    └ <function BaseProcess._bootstrap at 0x000001DD0A6D3560>
           └ <SpawnProcess name='SpawnProcess-1' parent=2832 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001DD0A67D3A0>
    └ <SpawnProcess name='SpawnProcess-1' parent=2832 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001DD0CAFB230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=2832 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=2832 started>
    │    └ <function subprocess_started at 0x000001DD0CBEF4C0>
    └ <SpawnProcess name='SpawnProcess-1' parent=2832 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=576, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001DD0CAFB380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=576, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001DD0CBEE3E0>
           │       │   └ <uvicorn.server.Server object at 0x000001DD0CAFB380>
           │       └ <function run at 0x000001DD0A9BDD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001DD0CBB26C0>
           │      └ <function Runner.run at 0x000001DD0C8EEF20>
           └ <asyncio.runners.Runner object at 0x000001DD0CC541A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001DD0C8ECA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001DD0CC541A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001DD0C8EC9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001DD0C8EE7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001DD0ADF5080>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001DD10AEF230>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD590>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001DD100CD2B0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001DD10AEF230>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD590>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD590>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001DD10AEFB60>
          └ <fastapi.applications.FastAPI object at 0x000001DD100CD2B0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001DD10E9B4C0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001DD10AEFA10>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001DD10AEFB60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '60', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001DD10E9B4C0>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001DD0DD47560>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001DD10AEFA10>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001DD10AEFA10...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001DD10AEF8C0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001DD10AEFA10>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001DD10AEFA10...
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001DD10ECC830>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001DD0FBF7A70>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001DD10AEF8C0>
          └ <function wrap_app_handling_exceptions at 0x000001DD0DCDA020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001DD10E9B6A0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001DD0FBF7A70>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001DD10E9B6A0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001DD0FBF7A70>>
          └ <fastapi.routing.APIRouter object at 0x000001DD0FBF7A70>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001DD10E9B6A0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001DD0DCDB920>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001DD10E9B6A0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001DD10D928E0>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001DD10E9B6A0>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001DD10EBD810>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001DD10E9B7E0>
          └ <function wrap_app_handling_exceptions at 0x000001DD0DCDA020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001DD10E9B920>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001DD10EBD...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001DD10E9B7E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001DD10EBD810>
                     └ <function get_request_handler.<locals>.app at 0x000001DD10D92840>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001DD0DCD9620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.orm.session.Session object at 0x000001DD10ECDD30>, 'user_login': UserLogin(username='admin', password='123...
                 │         └ <function login at 0x000001DD100E53A0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "E:\yingj\emergency-monitoring-system\backend\app\api\v1\auth.py", line 67, in login
    user = auth_service.authenticate_user(
           │            └ <function AuthService.authenticate_user at 0x000001DD100C9080>
           └ <app.services.auth_service.AuthService object at 0x000001DD10ECCD70>

  File "E:\yingj\emergency-monitoring-system\backend\app\services\auth_service.py", line 35, in authenticate_user
    if not verify_password(password, user.hashed_password):
           │               │         │    └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x000001DD0FD163E0>
           │               │         └ <app.models.user.User object at 0x000001DD10ECECF0>
           │               └ '123456'
           └ <function verify_password at 0x000001DD1006E2A0>

  File "E:\yingj\emergency-monitoring-system\backend\app\core\security.py", line 38, in verify_password
    return pwd_context.verify(plain_password, hashed_password)
           │           │      │               └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │           │      └ '123456'
           │           └ <function CryptContext.verify at 0x000001DD100563E0>
           └ <CryptContext at 0x1dd0ff670e0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\context.py", line 2347, in verify
    return record.verify(secret, hash, **kwds)
           │      │      │       │       └ {}
           │      │      │       └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │      │      └ '123456'
           │      └ <classmethod(<function GenericHandler.verify at 0x000001DD1001FC40>)>
           └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 792, in verify
    return consteq(self._calc_checksum(secret), chk)
           │       │    │              │        └ 'sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │       │    │              └ '123456'
           │       │    └ <function _NoBackend._calc_checksum at 0x000001DD1006D120>
           │       └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000001DD10ECEE40>
           └ <built-in function compare_digest>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
    │    └ <classmethod(<function BackendMixin._stub_requires_backend at 0x000001DD10049F80>)>
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000001DD10ECEE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
    │   └ <classmethod(<function BackendMixin.set_backend at 0x000001DD10049D00>)>
    └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           │     │           │            └ False
           │     │           └ 'any'
           │     └ <classmethod(<function BackendMixin.set_backend at 0x000001DD10049D00>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2163, in set_backend
    return cls.set_backend(name, dryrun=dryrun)
           │   │           │            └ False
           │   │           └ 'bcrypt'
           │   └ <classmethod(<function BackendMixin.set_backend at 0x000001DD10049D00>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2188, in set_backend
    cls._set_backend(name, dryrun)
    │   │            │     └ False
    │   │            └ 'bcrypt'
    │   └ <classmethod(<function SubclassBackendMixin._set_backend at 0x000001DD1004A0C0>)>
    └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2311, in _set_backend
    super(SubclassBackendMixin, cls)._set_backend(name, dryrun)
          │                     │                 │     └ False
          │                     │                 └ 'bcrypt'
          │                     └ <class 'passlib.handlers.bcrypt.bcrypt'>
          └ <class 'passlib.utils.handlers.SubclassBackendMixin'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2224, in _set_backend
    ok = loader(**kwds)
         │        └ {'name': 'bcrypt', 'dryrun': False}
         └ <bound method _BcryptBackend._load_backend_mixin of <class 'passlib.handlers.bcrypt._BcryptBackend'>>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              └ <module 'bcrypt' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bcrypt\\__i...

AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-04 13:06:16 | DEBUG    | logging:callHandlers:1736 | detected 'bcrypt' backend, version '<unknown>'
2025-08-04 13:06:16 | DEBUG    | logging:callHandlers:1736 | 'bcrypt' backend lacks $2$ support, enabling workaround
2025-08-04 13:06:17 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-04 13:06:17 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-04 13:06:31 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-04 13:06:31 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-04 13:06:31 | INFO     | logging:callHandlers:1736 |   公网地址: **************:9500
2025-08-04 13:29:09 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 13:30:08 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-04 13:30:08 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:16807
2025-08-04 13:30:08 | INFO     | logging:callHandlers:1736 |   公网地址: **************:16807
2025-08-04 13:30:59 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 13:31:19 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器启动成功
2025-08-04 13:31:19 | INFO     | logging:callHandlers:1736 |   监听地址: 0.0.0.0:9500
2025-08-04 13:31:19 | INFO     | logging:callHandlers:1736 |   公网地址: **************:9500
2025-08-04 14:18:05 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 14:59:04 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 14:59:06 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 14:59:45 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:12:28 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:14:14 | WARNING  | logging:callHandlers:1736 | (trapped) error reading bcrypt version
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=20640, pipe_handle=636)
                                      │           └ <function spawn_main at 0x000001C042E9CAE0>
                                      └ <function spawn_main at 0x000001C042E9CAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 312
               │     └ 3
               └ <function _main at 0x000001C042E9CB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 312
           │    └ <function BaseProcess._bootstrap at 0x000001C042BB3560>
           └ <SpawnProcess name='SpawnProcess-1' parent=20640 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001C042B5D3A0>
    └ <SpawnProcess name='SpawnProcess-1' parent=20640 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001C044FFB230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=20640 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=20640 started>
    │    └ <function subprocess_started at 0x000001C0450EF4C0>
    └ <SpawnProcess name='SpawnProcess-1' parent=20640 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=564, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001C044FFB380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=564, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001C0450EE3E0>
           │       │   └ <uvicorn.server.Server object at 0x000001C044FFB380>
           │       └ <function run at 0x000001C042E9DD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001C0450B26C0>
           │      └ <function Runner.run at 0x000001C044DEEF20>
           └ <asyncio.runners.Runner object at 0x000001C0451541A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001C044DECA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001C0451541A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001C044DEC9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001C044DEE7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001C044915080>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001C048FD7B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5590>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001C0485B52B0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001C048FD7B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5590>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5590>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001C0494FC590>
          └ <fastapi.applications.FastAPI object at 0x000001C0485B52B0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001C0494DDE40>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001C0494FC440>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001C0494FC590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '60', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001C0494DDE40>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001C046227560>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001C0494FC440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001C0494FC440...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001C0494FC2F0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001C0494FC440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001C0494FC440...
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001C0494F5310>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001C049383F20>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001C0494FC2F0>
          └ <function wrap_app_handling_exceptions at 0x000001C0461BA020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001C0494DE020>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001C049383F20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001C0494DE020>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001C049383F20>>
          └ <fastapi.routing.APIRouter object at 0x000001C049383F20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001C0494DE020>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001C0461BB920>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001C0494DE020>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001C049393F60>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001C0494DE020>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001C0491C7820>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001C0494DE160>
          └ <function wrap_app_handling_exceptions at 0x000001C0461BA020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001C0494DE2A0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001C0494F5...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001C0494DE160>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001C0491C7820>
                     └ <function get_request_handler.<locals>.app at 0x000001C049393EC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001C0461B9620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.orm.session.Session object at 0x000001C0494FC980>, 'user_login': UserLogin(username='admin', password='123...
                 │         └ <function login at 0x000001C0485CD3A0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "E:\yingj\emergency-monitoring-system\backend\app\api\v1\auth.py", line 67, in login
    user = auth_service.authenticate_user(
           │            └ <function AuthService.authenticate_user at 0x000001C0485B1080>
           └ <app.services.auth_service.AuthService object at 0x000001C0494FCEC0>

  File "E:\yingj\emergency-monitoring-system\backend\app\services\auth_service.py", line 35, in authenticate_user
    if not verify_password(password, user.hashed_password):
           │               │         │    └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x000001C04820A3E0>
           │               │         └ <app.models.user.User object at 0x000001C0494FE900>
           │               └ '123456'
           └ <function verify_password at 0x000001C0485562A0>

  File "E:\yingj\emergency-monitoring-system\backend\app\core\security.py", line 38, in verify_password
    return pwd_context.verify(plain_password, hashed_password)
           │           │      │               └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │           │      └ '123456'
           │           └ <function CryptContext.verify at 0x000001C0485363E0>
           └ <CryptContext at 0x1c04844f0e0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\context.py", line 2347, in verify
    return record.verify(secret, hash, **kwds)
           │      │      │       │       └ {}
           │      │      │       └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │      │      └ '123456'
           │      └ <classmethod(<function GenericHandler.verify at 0x000001C048507C40>)>
           └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 792, in verify
    return consteq(self._calc_checksum(secret), chk)
           │       │    │              │        └ 'sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │       │    │              └ '123456'
           │       │    └ <function _NoBackend._calc_checksum at 0x000001C048555120>
           │       └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000001C0494FEA50>
           └ <built-in function compare_digest>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
    │    └ <classmethod(<function BackendMixin._stub_requires_backend at 0x000001C048529F80>)>
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000001C0494FEA50>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
    │   └ <classmethod(<function BackendMixin.set_backend at 0x000001C048529D00>)>
    └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           │     │           │            └ False
           │     │           └ 'any'
           │     └ <classmethod(<function BackendMixin.set_backend at 0x000001C048529D00>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2163, in set_backend
    return cls.set_backend(name, dryrun=dryrun)
           │   │           │            └ False
           │   │           └ 'bcrypt'
           │   └ <classmethod(<function BackendMixin.set_backend at 0x000001C048529D00>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2188, in set_backend
    cls._set_backend(name, dryrun)
    │   │            │     └ False
    │   │            └ 'bcrypt'
    │   └ <classmethod(<function SubclassBackendMixin._set_backend at 0x000001C04852A0C0>)>
    └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2311, in _set_backend
    super(SubclassBackendMixin, cls)._set_backend(name, dryrun)
          │                     │                 │     └ False
          │                     │                 └ 'bcrypt'
          │                     └ <class 'passlib.handlers.bcrypt.bcrypt'>
          └ <class 'passlib.utils.handlers.SubclassBackendMixin'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2224, in _set_backend
    ok = loader(**kwds)
         │        └ {'name': 'bcrypt', 'dryrun': False}
         └ <bound method _BcryptBackend._load_backend_mixin of <class 'passlib.handlers.bcrypt._BcryptBackend'>>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              └ <module 'bcrypt' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bcrypt\\__i...

AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-04 15:14:14 | DEBUG    | logging:callHandlers:1736 | detected 'bcrypt' backend, version '<unknown>'
2025-08-04 15:14:14 | DEBUG    | logging:callHandlers:1736 | 'bcrypt' backend lacks $2$ support, enabling workaround
2025-08-04 15:14:14 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-04 15:14:14 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-04 15:14:18 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-04 15:14:18 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-04 15:17:29 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:18:12 | WARNING  | logging:callHandlers:1736 | (trapped) error reading bcrypt version
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=13424, pipe_handle=636)
                                      │           └ <function spawn_main at 0x00000187B195CAE0>
                                      └ <function spawn_main at 0x00000187B195CAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 320
               │     └ 3
               └ <function _main at 0x00000187B195CB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 320
           │    └ <function BaseProcess._bootstrap at 0x00000187B1673560>
           └ <SpawnProcess name='SpawnProcess-1' parent=13424 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000187B161D3A0>
    └ <SpawnProcess name='SpawnProcess-1' parent=13424 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000187B3A4B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=13424 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=13424 started>
    │    └ <function subprocess_started at 0x00000187B3B4F4C0>
    └ <SpawnProcess name='SpawnProcess-1' parent=13424 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=564, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x00000187B3A4B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=564, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x00000187B3B4E3E0>
           │       │   └ <uvicorn.server.Server object at 0x00000187B3A4B380>
           │       └ <function run at 0x00000187B195DD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000187B3B026C0>
           │      └ <function Runner.run at 0x00000187B381EF20>
           └ <asyncio.runners.Runner object at 0x00000187B3BB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000187B381CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000187B3BB41A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000187B381C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000187B381E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000187B3345080>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x00000187B7A47B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33820>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x00000187B70252B0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x00000187B7A47B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33820>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33820>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000187B7F6C590>
          └ <fastapi.applications.FastAPI object at 0x00000187B70252B0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x00000187B3B82DE0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000187B7F6C440>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000187B7F6C590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '60', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x00000187B3B82DE0>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x00000187B4CA7560>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000187B7F6C440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000187B7F6C440...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000187B7F6C2F0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000187B7F6C440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000187B7F6C440...
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000187B7F65450>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000187B7DF3F20>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000187B7F6C2F0>
          └ <function wrap_app_handling_exceptions at 0x00000187B4C3A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000187B7F49E40>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000187B7DF3F20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000187B7F49E40>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000187B7DF3F20>>
          └ <fastapi.routing.APIRouter object at 0x00000187B7DF3F20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000187B7F49E40>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x00000187B4C3B920>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000187B7F49E40>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000187B7E03F60>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000187B7F49E40>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000187B7C33A80>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000187B7F4A020>
          └ <function wrap_app_handling_exceptions at 0x00000187B4C3A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000187B7F4A2A0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000187B7C33...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x00000187B7F4A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000187B7C33A80>
                     └ <function get_request_handler.<locals>.app at 0x00000187B7E03EC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x00000187B4C39620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.orm.session.Session object at 0x00000187B7F6DE80>, 'user_login': UserLogin(username='admin', password='123...
                 │         └ <function login at 0x00000187B703D3A0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "E:\yingj\emergency-monitoring-system\backend\app\api\v1\auth.py", line 67, in login
    user = auth_service.authenticate_user(
           │            └ <function AuthService.authenticate_user at 0x00000187B7021080>
           └ <app.services.auth_service.AuthService object at 0x00000187B7F6CC20>

  File "E:\yingj\emergency-monitoring-system\backend\app\services\auth_service.py", line 35, in authenticate_user
    if not verify_password(password, user.hashed_password):
           │               │         │    └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x00000187B6C8A3E0>
           │               │         └ <app.models.user.User object at 0x00000187B7F6EE40>
           │               └ '123456'
           └ <function verify_password at 0x00000187B6FC62A0>

  File "E:\yingj\emergency-monitoring-system\backend\app\core\security.py", line 38, in verify_password
    return pwd_context.verify(plain_password, hashed_password)
           │           │      │               └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │           │      └ '123456'
           │           └ <function CryptContext.verify at 0x00000187B6FAA3E0>
           └ <CryptContext at 0x187b6ebf0e0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\context.py", line 2347, in verify
    return record.verify(secret, hash, **kwds)
           │      │      │       │       └ {}
           │      │      │       └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │      │      └ '123456'
           │      └ <classmethod(<function GenericHandler.verify at 0x00000187B6F77C40>)>
           └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 792, in verify
    return consteq(self._calc_checksum(secret), chk)
           │       │    │              │        └ 'sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │       │    │              └ '123456'
           │       │    └ <function _NoBackend._calc_checksum at 0x00000187B6FC5120>
           │       └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x00000187B7F6EF90>
           └ <built-in function compare_digest>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
    │    └ <classmethod(<function BackendMixin._stub_requires_backend at 0x00000187B6F9DF80>)>
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x00000187B7F6EF90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
    │   └ <classmethod(<function BackendMixin.set_backend at 0x00000187B6F9DD00>)>
    └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           │     │           │            └ False
           │     │           └ 'any'
           │     └ <classmethod(<function BackendMixin.set_backend at 0x00000187B6F9DD00>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2163, in set_backend
    return cls.set_backend(name, dryrun=dryrun)
           │   │           │            └ False
           │   │           └ 'bcrypt'
           │   └ <classmethod(<function BackendMixin.set_backend at 0x00000187B6F9DD00>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2188, in set_backend
    cls._set_backend(name, dryrun)
    │   │            │     └ False
    │   │            └ 'bcrypt'
    │   └ <classmethod(<function SubclassBackendMixin._set_backend at 0x00000187B6F9E0C0>)>
    └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2311, in _set_backend
    super(SubclassBackendMixin, cls)._set_backend(name, dryrun)
          │                     │                 │     └ False
          │                     │                 └ 'bcrypt'
          │                     └ <class 'passlib.handlers.bcrypt.bcrypt'>
          └ <class 'passlib.utils.handlers.SubclassBackendMixin'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2224, in _set_backend
    ok = loader(**kwds)
         │        └ {'name': 'bcrypt', 'dryrun': False}
         └ <bound method _BcryptBackend._load_backend_mixin of <class 'passlib.handlers.bcrypt._BcryptBackend'>>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              └ <module 'bcrypt' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bcrypt\\__i...

AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-04 15:18:12 | DEBUG    | logging:callHandlers:1736 | detected 'bcrypt' backend, version '<unknown>'
2025-08-04 15:18:12 | DEBUG    | logging:callHandlers:1736 | 'bcrypt' backend lacks $2$ support, enabling workaround
2025-08-04 15:18:12 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-04 15:18:12 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-04 15:20:00 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:20:02 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:20:40 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:20:42 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:21:04 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:21:06 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:21:59 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:22:00 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:22:32 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:22:34 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:23:10 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:23:12 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:23:57 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:23:59 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:30:23 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:30:50 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:32:03 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:36:50 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:38:52 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:38:54 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:39:08 | ERROR    | logging:callHandlers:1736 | 获取水尺概览数据失败: (psycopg2.errors.UndefinedTable) relation "water_level_devices" does not exist
LINE 3: FROM water_level_devices) AS anon_1
             ^

[SQL: SELECT count(*) AS count_1 
FROM (SELECT water_level_devices.id AS water_level_devices_id, water_level_devices.device_id AS water_level_devices_device_id, water_level_devices.name AS water_level_devices_name, water_level_devices.device_type AS water_level_devices_device_type, water_level_devices.status AS water_level_devices_status, water_level_devices.latitude AS water_level_devices_latitude, water_level_devices.longitude AS water_level_devices_longitude, water_level_devices.location_description AS water_level_devices_location_description, water_level_devices.sim_card AS water_level_devices_sim_card, water_level_devices.installation_date AS water_level_devices_installation_date, water_level_devices.last_maintenance AS water_level_devices_last_maintenance, water_level_devices.alert_water_level_high AS water_level_devices_alert_water_level_high, water_level_devices.alert_water_level_low AS water_level_devices_alert_water_level_low, water_level_devices.data_collection_interval AS water_level_devices_data_collection_interval, water_level_devices.is_active AS water_level_devices_is_active, water_level_devices.is_online AS water_level_devices_is_online, water_level_devices.last_data_time AS water_level_devices_last_data_time, water_level_devices.created_at AS water_level_devices_created_at, water_level_devices.updated_at AS water_level_devices_updated_at 
FROM water_level_devices) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-04 15:44:25 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:44:27 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:44:47 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:44:49 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:48:46 | ERROR    | logging:callHandlers:1736 | 获取水尺概览数据失败: (psycopg2.errors.UndefinedTable) relation "water_level_devices" does not exist
LINE 3: FROM water_level_devices) AS anon_1
             ^

[SQL: SELECT count(*) AS count_1 
FROM (SELECT water_level_devices.id AS water_level_devices_id, water_level_devices.device_id AS water_level_devices_device_id, water_level_devices.name AS water_level_devices_name, water_level_devices.device_type AS water_level_devices_device_type, water_level_devices.status AS water_level_devices_status, water_level_devices.latitude AS water_level_devices_latitude, water_level_devices.longitude AS water_level_devices_longitude, water_level_devices.location_description AS water_level_devices_location_description, water_level_devices.sim_card AS water_level_devices_sim_card, water_level_devices.installation_date AS water_level_devices_installation_date, water_level_devices.last_maintenance AS water_level_devices_last_maintenance, water_level_devices.alert_water_level_high AS water_level_devices_alert_water_level_high, water_level_devices.alert_water_level_low AS water_level_devices_alert_water_level_low, water_level_devices.data_collection_interval AS water_level_devices_data_collection_interval, water_level_devices.is_active AS water_level_devices_is_active, water_level_devices.is_online AS water_level_devices_is_online, water_level_devices.last_data_time AS water_level_devices_last_data_time, water_level_devices.created_at AS water_level_devices_created_at, water_level_devices.updated_at AS water_level_devices_updated_at 
FROM water_level_devices) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-04 15:51:17 | WARNING  | app.services.auth_service:authenticate_user:28 | 登录失败: 用户不存在 - lgx39252567
2025-08-04 15:51:17 | WARNING  | app.api.v1.auth:login:73 | 登录失败: lgx39252567 - IP: 127.0.0.1
2025-08-04 15:51:18 | WARNING  | app.services.auth_service:authenticate_user:28 | 登录失败: 用户不存在 - lgx39252567
2025-08-04 15:51:18 | WARNING  | app.api.v1.auth:login:73 | 登录失败: lgx39252567 - IP: 127.0.0.1
2025-08-04 15:51:28 | WARNING  | app.services.auth_service:authenticate_user:28 | 登录失败: 用户不存在 - lgx39252567
2025-08-04 15:51:28 | WARNING  | app.api.v1.auth:login:73 | 登录失败: lgx39252567 - IP: 127.0.0.1
2025-08-04 15:51:36 | WARNING  | app.services.auth_service:authenticate_user:28 | 登录失败: 用户不存在 - lgx39252567
2025-08-04 15:51:36 | WARNING  | app.api.v1.auth:login:73 | 登录失败: lgx39252567 - IP: 127.0.0.1
2025-08-04 15:57:02 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:57:04 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:57:33 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:57:34 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 15:58:07 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 15:58:09 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:03:19 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:03:20 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:04:09 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:04:11 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:04:28 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:04:30 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:07:04 | WARNING  | logging:callHandlers:1736 | (trapped) error reading bcrypt version
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=3056, pipe_handle=648)
                                      │           └ <function spawn_main at 0x000001E95208CAE0>
                                      └ <function spawn_main at 0x000001E95208CAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 192
               │     └ 3
               └ <function _main at 0x000001E95208CB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 192
           │    └ <function BaseProcess._bootstrap at 0x000001E951DA3560>
           └ <SpawnProcess name='SpawnProcess-10' parent=3056 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E951D4D3A0>
    └ <SpawnProcess name='SpawnProcess-10' parent=3056 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E95411B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-10' parent=3056 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-10' parent=3056 started>
    │    └ <function subprocess_started at 0x000001E95421F4C0>
    └ <SpawnProcess name='SpawnProcess-10' parent=3056 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=580, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E95411B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=580, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E95421E3E0>
           │       │   └ <uvicorn.server.Server object at 0x000001E95411B380>
           │       └ <function run at 0x000001E95208DD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E9541D26C0>
           │      └ <function Runner.run at 0x000001E953F0EF20>
           └ <asyncio.runners.Runner object at 0x000001E9542841A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E953F0CA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E9542841A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E953F0C9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E953F0E7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E952465080>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001E957E03CB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A210>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001E9577052B0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001E957E03CB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A210>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A210>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001E95867C6E0>
          └ <fastapi.applications.FastAPI object at 0x000001E9577052B0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001E954252F20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001E95867C590>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001E95867C6E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '60', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001E954252F20>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001E955377560>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001E95867C590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001E95867C590...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001E95867C440>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001E95867C590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001E95867C590...
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001E95867D2B0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001E9584FF2F0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001E95867C440>
          └ <function wrap_app_handling_exceptions at 0x000001E95530A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E95863FBA0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001E9584FF2F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E95863FBA0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001E9584FF2F0>>
          └ <fastapi.routing.APIRouter object at 0x000001E9584FF2F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E95863FBA0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000001E95530B920>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E95863FBA0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001E958519580>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E95863FBA0>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001E95864A490>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001E95863FEC0>
          └ <function wrap_app_handling_exceptions at 0x000001E95530A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E958684040>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E95864A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001E95863FEC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001E95864A490>
                     └ <function get_request_handler.<locals>.app at 0x000001E9585194E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001E955309620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.orm.session.Session object at 0x000001E95867E7B0>, 'user_login': UserLogin(username='admin', password='123...
                 │         └ <function login at 0x000001E95771D3A0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "E:\yingj\emergency-monitoring-system\backend\app\api\v1\auth.py", line 67, in login
    user = auth_service.authenticate_user(
           │            └ <function AuthService.authenticate_user at 0x000001E957701080>
           └ <app.services.auth_service.AuthService object at 0x000001E95867D7F0>

  File "E:\yingj\emergency-monitoring-system\backend\app\services\auth_service.py", line 35, in authenticate_user
    if not verify_password(password, user.hashed_password):
           │               │         │    └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x000001E95734A3E0>
           │               │         └ <app.models.user.User object at 0x000001E95867F770>
           │               └ '123456'
           └ <function verify_password at 0x000001E9576A62A0>

  File "E:\yingj\emergency-monitoring-system\backend\app\core\security.py", line 38, in verify_password
    return pwd_context.verify(plain_password, hashed_password)
           │           │      │               └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │           │      └ '123456'
           │           └ <function CryptContext.verify at 0x000001E95768A3E0>
           └ <CryptContext at 0x1e95759f0e0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\context.py", line 2347, in verify
    return record.verify(secret, hash, **kwds)
           │      │      │       │       └ {}
           │      │      │       └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │      │      └ '123456'
           │      └ <classmethod(<function GenericHandler.verify at 0x000001E957653C40>)>
           └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 792, in verify
    return consteq(self._calc_checksum(secret), chk)
           │       │    │              │        └ 'sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │       │    │              └ '123456'
           │       │    └ <function _NoBackend._calc_checksum at 0x000001E9576A5120>
           │       └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000001E95867F8C0>
           └ <built-in function compare_digest>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
    │    └ <classmethod(<function BackendMixin._stub_requires_backend at 0x000001E95767DF80>)>
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000001E95867F8C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
    │   └ <classmethod(<function BackendMixin.set_backend at 0x000001E95767DD00>)>
    └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           │     │           │            └ False
           │     │           └ 'any'
           │     └ <classmethod(<function BackendMixin.set_backend at 0x000001E95767DD00>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2163, in set_backend
    return cls.set_backend(name, dryrun=dryrun)
           │   │           │            └ False
           │   │           └ 'bcrypt'
           │   └ <classmethod(<function BackendMixin.set_backend at 0x000001E95767DD00>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2188, in set_backend
    cls._set_backend(name, dryrun)
    │   │            │     └ False
    │   │            └ 'bcrypt'
    │   └ <classmethod(<function SubclassBackendMixin._set_backend at 0x000001E95767E0C0>)>
    └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2311, in _set_backend
    super(SubclassBackendMixin, cls)._set_backend(name, dryrun)
          │                     │                 │     └ False
          │                     │                 └ 'bcrypt'
          │                     └ <class 'passlib.handlers.bcrypt.bcrypt'>
          └ <class 'passlib.utils.handlers.SubclassBackendMixin'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2224, in _set_backend
    ok = loader(**kwds)
         │        └ {'name': 'bcrypt', 'dryrun': False}
         └ <bound method _BcryptBackend._load_backend_mixin of <class 'passlib.handlers.bcrypt._BcryptBackend'>>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              └ <module 'bcrypt' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bcrypt\\__i...

AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-04 16:07:04 | DEBUG    | logging:callHandlers:1736 | detected 'bcrypt' backend, version '<unknown>'
2025-08-04 16:07:04 | DEBUG    | logging:callHandlers:1736 | 'bcrypt' backend lacks $2$ support, enabling workaround
2025-08-04 16:07:04 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-04 16:07:05 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-04 16:07:33 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-04 16:07:33 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | connect_tcp.started host='app.wlwapp.cn' port=80 local_address=None timeout=30.0 socket_options=None
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001E959F0FA10>
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | send_request_headers.started request=<Request [b'POST']>
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | send_request_headers.complete
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | send_request_body.started request=<Request [b'POST']>
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | send_request_body.complete
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | receive_response_headers.started request=<Request [b'POST']>
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'', [(b'Server', b'nginx'), (b'Date', b'Mon, 04 Aug 2025 08:07:53 GMT'), (b'Content-Type', b'application/json;charset=UTF-8'), (b'Content-Length', b'45'), (b'Set-Cookie', b'JSESSIONID=19652C5A30E4DB7FD61F9122742D4F79; Path=/; HttpOnly'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Credentials', b'true'), (b'Access-Control-Allow-Methods', b'*')])
2025-08-04 16:07:49 | INFO     | logging:callHandlers:1736 | HTTP Request: POST http://app.wlwapp.cn/api/v2/login "HTTP/1.1 200 "
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | receive_response_body.started request=<Request [b'POST']>
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | receive_response_body.complete
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | response_closed.started
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | response_closed.complete
2025-08-04 16:07:49 | ERROR    | logging:callHandlers:1736 | Login failed: 用户名或密码错误
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | close.started
2025-08-04 16:07:49 | DEBUG    | logging:callHandlers:1736 | close.complete
2025-08-04 16:13:30 | DEBUG    | logging:callHandlers:1736 | connect_tcp.started host='app.wlwapp.cn' port=80 local_address=None timeout=30.0 socket_options=None
2025-08-04 16:13:30 | DEBUG    | logging:callHandlers:1736 | connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001E959F079D0>
2025-08-04 16:13:30 | DEBUG    | logging:callHandlers:1736 | send_request_headers.started request=<Request [b'POST']>
2025-08-04 16:13:30 | DEBUG    | logging:callHandlers:1736 | send_request_headers.complete
2025-08-04 16:13:30 | DEBUG    | logging:callHandlers:1736 | send_request_body.started request=<Request [b'POST']>
2025-08-04 16:13:30 | DEBUG    | logging:callHandlers:1736 | send_request_body.complete
2025-08-04 16:13:30 | DEBUG    | logging:callHandlers:1736 | receive_response_headers.started request=<Request [b'POST']>
2025-08-04 16:13:31 | DEBUG    | logging:callHandlers:1736 | receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'', [(b'Server', b'nginx'), (b'Date', b'Mon, 04 Aug 2025 08:13:32 GMT'), (b'Content-Type', b'application/json;charset=UTF-8'), (b'Content-Length', b'45'), (b'Set-Cookie', b'JSESSIONID=E97A10BBA95F206F0CF1C832B2EE51F7; Path=/; HttpOnly'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Credentials', b'true'), (b'Access-Control-Allow-Methods', b'*')])
2025-08-04 16:13:31 | INFO     | logging:callHandlers:1736 | HTTP Request: POST http://app.wlwapp.cn/api/v2/login "HTTP/1.1 200 "
2025-08-04 16:13:31 | DEBUG    | logging:callHandlers:1736 | receive_response_body.started request=<Request [b'POST']>
2025-08-04 16:13:31 | DEBUG    | logging:callHandlers:1736 | receive_response_body.complete
2025-08-04 16:13:31 | DEBUG    | logging:callHandlers:1736 | response_closed.started
2025-08-04 16:13:31 | DEBUG    | logging:callHandlers:1736 | response_closed.complete
2025-08-04 16:13:31 | ERROR    | logging:callHandlers:1736 | Login failed: 用户名或密码错误
2025-08-04 16:13:31 | DEBUG    | logging:callHandlers:1736 | close.started
2025-08-04 16:13:31 | DEBUG    | logging:callHandlers:1736 | close.complete
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | connect_tcp.started host='app.wlwapp.cn' port=80 local_address=None timeout=30.0 socket_options=None
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001E959FCA5D0>
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | send_request_headers.started request=<Request [b'POST']>
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | send_request_headers.complete
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | send_request_body.started request=<Request [b'POST']>
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | send_request_body.complete
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | receive_response_headers.started request=<Request [b'POST']>
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'', [(b'Server', b'nginx'), (b'Date', b'Mon, 04 Aug 2025 08:13:45 GMT'), (b'Content-Type', b'application/json;charset=UTF-8'), (b'Content-Length', b'45'), (b'Set-Cookie', b'JSESSIONID=B85BCB06E043FD418E8A13619BC7BFD4; Path=/; HttpOnly'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Credentials', b'true'), (b'Access-Control-Allow-Methods', b'*')])
2025-08-04 16:13:44 | INFO     | logging:callHandlers:1736 | HTTP Request: POST http://app.wlwapp.cn/api/v2/login "HTTP/1.1 200 "
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | receive_response_body.started request=<Request [b'POST']>
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | receive_response_body.complete
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | response_closed.started
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | response_closed.complete
2025-08-04 16:13:44 | ERROR    | logging:callHandlers:1736 | Login failed: 用户名或密码错误
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | close.started
2025-08-04 16:13:44 | DEBUG    | logging:callHandlers:1736 | close.complete
2025-08-04 16:19:45 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:19:47 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:20:03 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:20:05 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:20:20 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:20:22 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:20:34 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:20:36 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:22:06 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:22:08 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | connect_tcp.started host='app.wlwapp.cn' port=80 local_address=None timeout=30.0 socket_options=None
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000130518B2660>
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | send_request_headers.started request=<Request [b'POST']>
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | send_request_headers.complete
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | send_request_body.started request=<Request [b'POST']>
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | send_request_body.complete
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | receive_response_headers.started request=<Request [b'POST']>
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'', [(b'Server', b'nginx'), (b'Date', b'Mon, 04 Aug 2025 08:22:48 GMT'), (b'Content-Type', b'application/json;charset=UTF-8'), (b'Content-Length', b'213'), (b'Set-Cookie', b'JSESSIONID=42338CA6918EB9E5833F38E6929C32D3; Path=/; HttpOnly'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Credentials', b'true'), (b'Access-Control-Allow-Methods', b'*')])
2025-08-04 16:22:47 | INFO     | logging:callHandlers:1736 | HTTP Request: POST http://app.wlwapp.cn/api/v2/login "HTTP/1.1 200 "
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | receive_response_body.started request=<Request [b'POST']>
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | receive_response_body.complete
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | response_closed.started
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | response_closed.complete
2025-08-04 16:22:47 | INFO     | logging:callHandlers:1736 | Water level API login successful
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | close.started
2025-08-04 16:22:47 | DEBUG    | logging:callHandlers:1736 | close.complete
2025-08-04 16:23:08 | DEBUG    | logging:callHandlers:1736 | connect_tcp.started host='app.wlwapp.cn' port=80 local_address=None timeout=30.0 socket_options=None
2025-08-04 16:23:08 | DEBUG    | logging:callHandlers:1736 | connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000013051902490>
2025-08-04 16:23:08 | DEBUG    | logging:callHandlers:1736 | send_request_headers.started request=<Request [b'POST']>
2025-08-04 16:23:08 | DEBUG    | logging:callHandlers:1736 | send_request_headers.complete
2025-08-04 16:23:08 | DEBUG    | logging:callHandlers:1736 | send_request_body.started request=<Request [b'POST']>
2025-08-04 16:23:08 | DEBUG    | logging:callHandlers:1736 | send_request_body.complete
2025-08-04 16:23:08 | DEBUG    | logging:callHandlers:1736 | receive_response_headers.started request=<Request [b'POST']>
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'', [(b'Server', b'nginx'), (b'Date', b'Mon, 04 Aug 2025 08:23:10 GMT'), (b'Content-Type', b'application/json;charset=UTF-8'), (b'Content-Length', b'213'), (b'Set-Cookie', b'JSESSIONID=7B2BE2DFFA3337F8DC5EFC1E1B7E8155; Path=/; HttpOnly'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Credentials', b'true'), (b'Access-Control-Allow-Methods', b'*')])
2025-08-04 16:23:09 | INFO     | logging:callHandlers:1736 | HTTP Request: POST http://app.wlwapp.cn/api/v2/login "HTTP/1.1 200 "
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | receive_response_body.started request=<Request [b'POST']>
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | receive_response_body.complete
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | response_closed.started
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | response_closed.complete
2025-08-04 16:23:09 | INFO     | logging:callHandlers:1736 | Water level API login successful
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | close.started
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | close.complete
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | connect_tcp.started host='app.wlwapp.cn' port=80 local_address=None timeout=30.0 socket_options=None
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000130519191D0>
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | send_request_headers.started request=<Request [b'POST']>
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | send_request_headers.complete
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | send_request_body.started request=<Request [b'POST']>
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | send_request_body.complete
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | receive_response_headers.started request=<Request [b'POST']>
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'', [(b'Server', b'nginx'), (b'Date', b'Mon, 04 Aug 2025 08:23:10 GMT'), (b'Content-Type', b'application/json;charset=UTF-8'), (b'Content-Length', b'375'), (b'Set-Cookie', b'JSESSIONID=1527CD9564DAE17D731A44408401C97E; Path=/; HttpOnly'), (b'Access-Control-Allow-Origin', b'*'), (b'Access-Control-Allow-Credentials', b'true'), (b'Access-Control-Allow-Methods', b'*')])
2025-08-04 16:23:09 | INFO     | logging:callHandlers:1736 | HTTP Request: POST http://app.wlwapp.cn/api/v2/device "HTTP/1.1 200 "
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | receive_response_body.started request=<Request [b'POST']>
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | receive_response_body.complete
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | response_closed.started
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | response_closed.complete
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | close.started
2025-08-04 16:23:09 | DEBUG    | logging:callHandlers:1736 | close.complete
2025-08-04 16:23:51 | ERROR    | app.services.device_service:_create_water_level_device:486 | 创建电子水尺设备失败: asyncio.run() cannot be called from a running event loop
2025-08-04 16:30:47 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:30:49 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:31:04 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:31:06 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:32:13 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:32:14 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:34:55 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:34:57 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:35:15 | INFO     | logging:callHandlers:1736 | 摄像头注册服务器已停止
2025-08-04 16:43:25 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:43:25 | INFO     | app.utils.logger:setup_logging:102 | 日志系统初始化完成
2025-08-04 16:43:26 | WARNING  | logging:callHandlers:1736 | (trapped) error reading bcrypt version
Traceback (most recent call last):

  File "<string>", line 1, in <module>
    from multiprocessing.spawn import spawn_main; spawn_main(parent_pid=3056, pipe_handle=648)
                                      │           └ <function spawn_main at 0x0000023368EDCAE0>
                                      └ <function spawn_main at 0x0000023368EDCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 336
               │     └ 3
               └ <function _main at 0x0000023368EDCB80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 336
           │    └ <function BaseProcess._bootstrap at 0x0000023368BF3560>
           └ <SpawnProcess name='SpawnProcess-26' parent=3056 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000023368B9D3A0>
    └ <SpawnProcess name='SpawnProcess-26' parent=3056 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000002336B00B230>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-26' parent=3056 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-26' parent=3056 started>
    │    └ <function subprocess_started at 0x000002336B0FF4C0>
    └ <SpawnProcess name='SpawnProcess-26' parent=3056 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=548, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000002336B00B380>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=548, family=2, type=1, proto=6, laddr=('127.0.0.1', 8000)>]
           │       │   │    └ <function Server.serve at 0x000002336B0FE3E0>
           │       │   └ <uvicorn.server.Server object at 0x000002336B00B380>
           │       └ <function run at 0x0000023368EDDD00>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002336B0C26C0>
           │      └ <function Runner.run at 0x000002336ADFEF20>
           └ <asyncio.runners.Runner object at 0x000002336B1641A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002336ADFCA40>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002336B1641A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000002336ADFC9A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002336ADFE7A0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002336A925080>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002336F50C050>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A450>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002336E5852B0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002336F50C050>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A450>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A450>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002336F50C980>
          └ <fastapi.applications.FastAPI object at 0x000002336E5852B0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002336F504F40>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002336F50C830>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002336F50C980>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '60', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002336F504F40>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000002336C207560>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002336F50C830>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002336F50C830...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002336F50C6E0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002336F50C830>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002336F50C830...
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002336F50D550>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002336F398C80>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002336F50C6E0>
          └ <function wrap_app_handling_exceptions at 0x000002336C19A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002336F5059E0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002336F398C80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002336F5059E0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002336F398C80>>
          └ <fastapi.routing.APIRouter object at 0x000002336F398C80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002336F5059E0>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x000002336C19B920>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002336F5059E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000002336F396480>
          └ APIRoute(path='/api/v1/auth/login', name='login', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002336F5059E0>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000002336F4CF610>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002336F505A80>
          └ <function wrap_app_handling_exceptions at 0x000002336C19A020>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002336F505BC0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002336F47A...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002336F505A80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000002336F4CF610>
                     └ <function get_request_handler.<locals>.app at 0x000002336F3963E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000002336C199620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'db': <sqlalchemy.orm.session.Session object at 0x000002336F50EF90>, 'user_login': UserLogin(username='admin', password='123...
                 │         └ <function login at 0x000002336E59D3A0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "E:\yingj\emergency-monitoring-system\backend\app\api\v1\auth.py", line 67, in login
    user = auth_service.authenticate_user(
           │            └ <function AuthService.authenticate_user at 0x000002336E581080>
           └ <app.services.auth_service.AuthService object at 0x000002336F50DA90>

  File "E:\yingj\emergency-monitoring-system\backend\app\services\auth_service.py", line 35, in authenticate_user
    if not verify_password(password, user.hashed_password):
           │               │         │    └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x000002336E1C63E0>
           │               │         └ <app.models.user.User object at 0x000002336F50F8C0>
           │               └ '123456'
           └ <function verify_password at 0x000002336E5262A0>

  File "E:\yingj\emergency-monitoring-system\backend\app\core\security.py", line 38, in verify_password
    return pwd_context.verify(plain_password, hashed_password)
           │           │      │               └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │           │      └ '123456'
           │           └ <function CryptContext.verify at 0x000002336E50A3E0>
           └ <CryptContext at 0x2336e41f0e0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\context.py", line 2347, in verify
    return record.verify(secret, hash, **kwds)
           │      │      │       │       └ {}
           │      │      │       └ '$2b$12$0xs0CahrbWnXmbRsteRLS.sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │      │      └ '123456'
           │      └ <classmethod(<function GenericHandler.verify at 0x000002336E4D3C40>)>
           └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 792, in verify
    return consteq(self._calc_checksum(secret), chk)
           │       │    │              │        └ 'sDcfHjqPUezwOlgvs9ByvnbRMvxKEMW'
           │       │    │              └ '123456'
           │       │    └ <function _NoBackend._calc_checksum at 0x000002336E525120>
           │       └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000002336F50FA10>
           └ <built-in function compare_digest>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 591, in _calc_checksum
    self._stub_requires_backend()
    │    └ <classmethod(<function BackendMixin._stub_requires_backend at 0x000002336E4FDF80>)>
    └ <passlib.handlers.bcrypt.<customized bcrypt hasher> object at 0x000002336F50FA10>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2254, in _stub_requires_backend
    cls.set_backend()
    │   └ <classmethod(<function BackendMixin.set_backend at 0x000002336E4FDD00>)>
    └ <class 'passlib.handlers.bcrypt.<customized bcrypt hasher>'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2156, in set_backend
    return owner.set_backend(name, dryrun=dryrun)
           │     │           │            └ False
           │     │           └ 'any'
           │     └ <classmethod(<function BackendMixin.set_backend at 0x000002336E4FDD00>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2163, in set_backend
    return cls.set_backend(name, dryrun=dryrun)
           │   │           │            └ False
           │   │           └ 'bcrypt'
           │   └ <classmethod(<function BackendMixin.set_backend at 0x000002336E4FDD00>)>
           └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2188, in set_backend
    cls._set_backend(name, dryrun)
    │   │            │     └ False
    │   │            └ 'bcrypt'
    │   └ <classmethod(<function SubclassBackendMixin._set_backend at 0x000002336E4FE0C0>)>
    └ <class 'passlib.handlers.bcrypt.bcrypt'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2311, in _set_backend
    super(SubclassBackendMixin, cls)._set_backend(name, dryrun)
          │                     │                 │     └ False
          │                     │                 └ 'bcrypt'
          │                     └ <class 'passlib.handlers.bcrypt.bcrypt'>
          └ <class 'passlib.utils.handlers.SubclassBackendMixin'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\utils\handlers.py", line 2224, in _set_backend
    ok = loader(**kwds)
         │        └ {'name': 'bcrypt', 'dryrun': False}
         └ <bound method _BcryptBackend._load_backend_mixin of <class 'passlib.handlers.bcrypt._BcryptBackend'>>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              └ <module 'bcrypt' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\bcrypt\\__i...

AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-04 16:43:26 | DEBUG    | logging:callHandlers:1736 | detected 'bcrypt' backend, version '<unknown>'
2025-08-04 16:43:26 | DEBUG    | logging:callHandlers:1736 | 'bcrypt' backend lacks $2$ support, enabling workaround
2025-08-04 16:43:26 | INFO     | app.services.auth_service:authenticate_user:43 | 用户登录成功 - admin
2025-08-04 16:43:26 | INFO     | app.api.v1.auth:login:96 | 用户登录成功: admin - IP: 127.0.0.1
