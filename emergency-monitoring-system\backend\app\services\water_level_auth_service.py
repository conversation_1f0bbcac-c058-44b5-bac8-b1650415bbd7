"""
电子水尺API认证服务
管理电子水尺API的token获取和存储
"""

import hashlib
import httpx
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.user import User

logger = logging.getLogger(__name__)

class WaterLevelAuthService:
    """电子水尺API认证服务"""
    
    def __init__(self):
        self.base_url = "http://app.wlwapp.cn/api/v2"
        # 存储用户的API凭据和token（实际应用中应该加密存储）
        self._user_credentials = {}
        self._user_tokens = {}
    
    def _md5_encrypt(self, text: str) -> str:
        """MD5加密 - 32位小写"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    async def login_and_get_token(self, username: str, password: str, user_id: int) -> Dict[str, Any]:
        """
        登录电子水尺API并获取token
        
        Args:
            username: 电子水尺API用户名
            password: 电子水尺API密码（明文）
            user_id: 系统用户ID
            
        Returns:
            包含成功状态、token和过期时间的字典
        """
        try:
            # MD5加密密码
            encrypted_password = self._md5_encrypt(password)
            
            # 调用登录API
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/login",
                    json={
                        "username": username,
                        "password": encrypted_password
                    },
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("status") == 1:
                        token_data = data.get("data", {})
                        token = token_data.get("token")
                        timeout_timestamp = token_data.get("timeout")
                        
                        if token and timeout_timestamp:
                            # 计算过期时间
                            expires_at = datetime.fromtimestamp(timeout_timestamp / 1000)
                            
                            # 存储用户凭据和token
                            self._user_credentials[user_id] = {
                                "username": username,
                                "password": password,  # 实际应用中应该加密存储
                                "encrypted_password": encrypted_password
                            }
                            
                            self._user_tokens[user_id] = {
                                "token": token,
                                "expires_at": expires_at,
                                "created_at": datetime.now()
                            }
                            
                            logger.info(f"用户 {user_id} 电子水尺API登录成功")
                            
                            return {
                                "success": True,
                                "token": token,
                                "expires_at": expires_at.isoformat(),
                                "message": "登录成功"
                            }
                        else:
                            return {
                                "success": False,
                                "error": "未获取到有效token",
                                "error_code": "INVALID_TOKEN"
                            }
                    else:
                        error_msg = data.get('msg', '登录失败')
                        return {
                            "success": False,
                            "error": error_msg,
                            "error_code": "LOGIN_FAILED"
                        }
                else:
                    return {
                        "success": False,
                        "error": f"请求失败，状态码: {response.status_code}",
                        "error_code": "REQUEST_FAILED"
                    }
                    
        except httpx.TimeoutException:
            return {
                "success": False,
                "error": "连接超时，请检查网络连接",
                "error_code": "TIMEOUT"
            }
        except Exception as e:
            logger.error(f"电子水尺API登录错误: {str(e)}")
            return {
                "success": False,
                "error": f"连接错误: {str(e)}",
                "error_code": "CONNECTION_ERROR"
            }
    
    def get_user_token(self, user_id: int) -> Optional[str]:
        """
        获取用户的有效token
        
        Args:
            user_id: 系统用户ID
            
        Returns:
            有效的token或None
        """
        token_info = self._user_tokens.get(user_id)
        if not token_info:
            return None
        
        # 检查token是否过期
        if datetime.now() >= token_info["expires_at"]:
            logger.warning(f"用户 {user_id} 的电子水尺API token已过期")
            return None
        
        return token_info["token"]
    
    async def refresh_token_if_needed(self, user_id: int) -> Optional[str]:
        """
        如果需要，刷新用户的token
        
        Args:
            user_id: 系统用户ID
            
        Returns:
            有效的token或None
        """
        token_info = self._user_tokens.get(user_id)
        credentials = self._user_credentials.get(user_id)
        
        if not token_info or not credentials:
            return None
        
        # 如果token即将过期（提前5分钟刷新），重新登录
        if datetime.now() >= (token_info["expires_at"] - timedelta(minutes=5)):
            logger.info(f"刷新用户 {user_id} 的电子水尺API token")
            result = await self.login_and_get_token(
                credentials["username"],
                credentials["password"],
                user_id
            )
            
            if result["success"]:
                return result["token"]
            else:
                logger.error(f"刷新token失败: {result.get('error')}")
                return None
        
        return token_info["token"]
    
    def has_user_credentials(self, user_id: int) -> bool:
        """检查用户是否已配置电子水尺API凭据"""
        return user_id in self._user_credentials
    
    def remove_user_credentials(self, user_id: int):
        """移除用户的电子水尺API凭据和token"""
        self._user_credentials.pop(user_id, None)
        self._user_tokens.pop(user_id, None)
        logger.info(f"已移除用户 {user_id} 的电子水尺API凭据")

# 全局实例
water_level_auth_service = WaterLevelAuthService()
