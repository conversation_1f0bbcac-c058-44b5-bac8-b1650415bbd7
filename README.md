# 应急安全监控系统 - 项目文档中心

## 🎯 项目概述

应急安全监控系统是一个实时监控汛期水位及森林消防安全的应急系统，集成摄像头视频流分析、电子水尺数据采集、AI图像识别报警等功能，实现多设备协同监控与可视化预警管理。

### 核心特性
- 🎥 **35个摄像头实时监控** - 支持跨网段设备接入
- 🤖 **AI智能图像识别** - qwen2.5vl模型自动检测异常
- 🌊 **水位监测集成** - 电子水尺数据实时采集
- 🗺️ **地理可视化** - 地图展示设备分布和状态
- ⚡ **实时报警推送** - 多渠道通知，快速响应
- 🔧 **双网络模式** - 开发环境代理模式，生产环境公网模式

## 📚 文档导航

### 🚀 快速开始
| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [快速开始指南](快速开始指南.md) | 系统部署和基本使用 | 所有用户 |
| [NetSDK安装指南](NetSDK安装指南.md) | 摄像头SDK安装步骤 | 开发者 |
| [本地开发Docker部署配置](本地开发Docker部署配置.md) | 开发环境搭建 | 开发者 |

### 📋 需求和设计
| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [应急安全监控系统开发需求文档](应急安全监控系统开发需求文档.md) | 完整功能需求和架构设计 | 项目经理、开发者 |
| [应急安全监控系统开发文档](应急安全监控系统开发文档.md) | 技术架构和实现方案 | 开发者、架构师 |
| [项目结构示例](项目结构示例.md) | 代码组织和目录结构 | 开发者 |

### 🔧 技术实现
| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [核心代码示例](核心代码示例.md) | 后端核心功能实现 | 后端开发者 |
| [前端代码示例](前端代码示例.md) | 前端组件和功能实现 | 前端开发者 |
| [网络架构配置指南](网络架构配置指南.md) | 跨网段搜索和网络配置 | 开发者、运维 |
| [配置文件模板](配置文件模板.md) | 环境配置和部署模板 | 开发者、运维 |

### 👥 项目管理
| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [项目管理和协作指南](项目管理和协作指南.md) | 团队协作和开发流程 | 项目经理、团队成员 |

## 🏗️ 技术架构

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 React    │    │   后端 FastAPI  │    │   数据存储层    │
│                 │    │                 │    │                 │
│ ├─ 监控大屏     │    │ ├─ 设备管理API  │    │ ├─ PostgreSQL   │
│ ├─ 地图可视化   │◄──►│ ├─ 视频流API    │◄──►│ ├─ TimescaleDB  │
│ ├─ 报警管理     │    │ ├─ AI分析API    │    │ ├─ Redis        │
│ ├─ 用户管理     │    │ ├─ 报警推送API  │    │ └─ MinIO        │
│ └─ 系统设置     │    │ └─ WebSocket    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈
- **后端**: FastAPI + Python 3.9+ + PostgreSQL + TimescaleDB + Redis + Celery
- **前端**: React 18 + TypeScript + Ant Design + React-Leaflet + Video.js
- **摄像头**: NetSDK Python *******
- **AI服务**: qwen2.5vl模型
- **部署**: Docker + Kubernetes + MinIO

## 🚀 快速部署

### 生产环境一键部署
```bash
# 1. 克隆项目
git clone <repository-url>
cd emergency-monitoring-system

# 2. 配置环境
cp .env.prod.example .env
# 编辑 .env 文件，填入具体配置

# 3. 启动服务
docker-compose up -d

# 4. 初始化数据库
docker-compose exec api python scripts/init_db.py
docker-compose exec api python scripts/create_admin.py
```

### 开发环境搭建
```bash
# 1. 安装依赖
./scripts/setup-config.sh dev
./scripts/dev-setup.sh

# 2. 安装NetSDK
pip install NetSDK-*******-py3-none-win_amd64.whl

# 3. 启动开发服务
./scripts/dev-start.sh
```

## 📊 项目状态

### 开发进度
- [x] **第一阶段**: 基础架构搭建 ✅
- [ ] **第二阶段**: 摄像头集成与视频功能 🚧
- [ ] **第三阶段**: AI分析与报警系统 📋
- [ ] **第四阶段**: 地图可视化与监控大屏 📋
- [ ] **第五阶段**: 系统优化与部署 📋

### 功能模块状态
| 模块 | 状态 | 完成度 | 负责人 |
|------|------|--------|--------|
| 用户管理 | ✅ 完成 | 100% | 后端团队 |
| 设备管理 | ✅ 完成 | 100% | 后端团队 |
| 摄像头集成 | ✅ 完成 | 100% | 后端团队 |
| 视频流播放 | 🚧 开发中 | 40% | 前端团队 |
| AI图像识别 | 📋 计划中 | 0% | 后端团队 |
| 报警系统 | 📋 计划中 | 0% | 全栈团队 |
| 地图可视化 | ✅ 完成 | 100% | 前端团队 |
| 监控大屏 | 📋 计划中 | 0% | 前端团队 |

## 🔗 相关链接

### 开发环境
- **前端开发服务器**: http://localhost:3000
- **后端API服务**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **MinIO控制台**: http://localhost:9001

### 生产环境
- **系统访问地址**: https://monitoring.example.com
- **API接口地址**: https://api.monitoring.example.com
- **监控面板**: https://grafana.monitoring.example.com

## 🤝 贡献指南

### 开发流程
1. **Fork项目** 并创建功能分支
2. **编写代码** 并确保通过所有测试
3. **提交代码** 遵循提交规范
4. **创建Pull Request** 并等待代码审查
5. **合并代码** 审查通过后合并

### 代码规范
- **Python**: 使用Black格式化，flake8检查
- **TypeScript**: 使用Prettier格式化，ESLint检查
- **提交信息**: 遵循Conventional Commits规范
- **测试覆盖率**: 新代码测试覆盖率>80%

# mcp使用
- **Context 7**: 提供技术细节支持
- **Playwright**: 检查前端实际效果
- **21st-dev/magic**: 负责前端UI的设计

### 问题反馈
- **Bug报告**: 使用Issue模板报告问题
- **功能请求**: 详细描述需求和使用场景
- **技术讨论**: 在Discussion区域进行技术交流

## 📞 联系方式

### 项目团队
- **项目经理**: [姓名] - [邮箱]
- **技术负责人**: [姓名] - [邮箱]
- **产品负责人**: [姓名] - [邮箱]

### 技术支持
- **开发问题**: 提交Issue或联系开发团队
- **部署问题**: 查看部署文档或联系运维团队
- **使用问题**: 查看用户手册或联系产品团队

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 🙏 致谢

感谢以下开源项目和技术社区的支持：
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [React](https://reactjs.org/) - 用户界面构建库
- [Ant Design](https://ant.design/) - 企业级UI设计语言
- [PostgreSQL](https://www.postgresql.org/) - 开源关系型数据库
- [TimescaleDB](https://www.timescale.com/) - 时序数据库扩展
- [Docker](https://www.docker.com/) - 容器化平台

---

**最后更新**: 2025-01-09  
**文档版本**: v1.0.0  
**项目状态**: 开发中 🚧
